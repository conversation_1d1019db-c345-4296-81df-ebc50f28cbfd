from odoo import api, models, fields
import logging

_logger = logging.getLogger(__name__)


class PosOrder(models.Model):
    _inherit = "pos.order"

    @api.model
    def get_table_draft_orders(self, table_id):
        table_orders = super().get_table_draft_orders(table_id)
        print(table_orders)
        for order in table_orders:
            order['billNo'] = order['name']

        return table_orders

    def _export_for_ui(self, order):
        result = super(PosOrder, self)._export_for_ui(order)
        result['billNo'] = order.name
        return result

    def add_print_no(self, vals):
        self.no_of_prints = vals['no_of_prints']
        print(self.id, self.name, self.pos_reference)
        print(vals)