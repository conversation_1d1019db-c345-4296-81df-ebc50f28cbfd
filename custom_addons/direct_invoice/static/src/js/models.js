/** @odoo-module **/

import { Order } from "@point_of_sale/app/store/models";
import { PosStore } from "@point_of_sale/app/store/pos_store";
import { patch } from "@web/core/utils/patch";

patch(Order.prototype, {
    setup() {
        super.setup(...arguments);
        this.to_invoice = true;
    },
    
    is_to_invoice() {
        return true;
    },
    
    set_to_invoice(to_invoice) {
        // Always force to_invoice to be true
        this.to_invoice = true;
    },
    
    init_from_JSON(json) {
        super.init_from_JSON(json);
        this.billNo = json.billNo;
        this.to_invoice = true; // Force invoice to true when loading from JSON
    },
    
    export_as_JSON() {
        const json = super.export_as_JSON();
        json.billNo = this.billNo;
        json.to_invoice = true; // Always export with invoice = true
        return json;
    },
    
    export_for_printing() {
        const receipt = super.export_for_printing();
        receipt.billNo = this.billNo;
        return receipt;
    },
});

patch(PosStore.prototype, {
    async push_and_invoice_order(order) {
        const self = this;
        return new Promise((resolve, reject) => {
            if (!order.get_partner()) {
                reject({code: 400, message: 'Missing Customer', data: {}});
            } else {
                const order_id = self.db.add_order(order.export_as_JSON());
                self.flush_mutex.exec(async () => {
                    try {
                        const server_ids = await self._flush_orders([self.db.get_order(order_id)], {
                            timeout: 30000,
                            to_invoice: true,
                        });
                        if (server_ids.length) {
                            const [orderWithInvoice] = await self.orm.call(
                                'pos.order',
                                'read',
                                [server_ids, ['account_move', 'name']],
                                {load: false}
                            ).then((data) => {
                                order["billNo"] = data[0]["name"];
                                return data;
                            });
                        } else {
                            reject({code: 401, message: 'Backend Invoice', data: {order: order}});
                        }
                        resolve(server_ids);
                    } catch (error) {
                        reject(error);
                    }
                });
            }
        });
    },
    
    setup() {
        super.setup(...arguments);
        this.billNo = null;
    },
});