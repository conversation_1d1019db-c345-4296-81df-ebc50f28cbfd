/** @odoo-module */

import { Component } from "@odoo/owl";
import { usePos } from "@point_of_sale/app/store/pos_hook";

export class CoolButton extends Component {
    static template = "pos_restaurant_custom_button.CoolButton";

    setup() {
        this.pos = usePos();
        console.log('Cool button component setup!');
    }

    get buttonClass() {
        return "btn btn-secondary btn-lg py-5";
    }

    onClick() {
        console.log('Cool button clicked!');
        window.alert("Hey, I'm cool! 😎");
    }
}
