/** @odoo-module */

import { registry } from "@web/core/registry";
import { CoolButton } from "./cool_button";

console.log('%c POS CUSTOM BUTTON MODULE LOADED! ', 'background: #222; color: #bada55; font-size: 24px;');

// Register the button in the POS control buttons registry
registry.category("pos_control_buttons").add("CoolButton", {
    component: CoolButton,
    position: "right",
    condition: () => true,
});
