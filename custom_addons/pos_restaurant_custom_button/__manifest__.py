{
    'name': 'POS Restaurant Custom Button',
    'version': '1.0',
    'category': 'Point of Sale',
    'sequence': 10,
    'summary': 'Add custom button to POS Restaurant order screen',
    'description': """
        This module adds a custom button to the POS Restaurant order screen.
    """,
    'depends': ['point_of_sale', 'pos_restaurant'],
    'data': [],
    'assets': {
        'point_of_sale._assets_pos': [
            'pos_restaurant_custom_button/static/src/app/screens/product_screen/control_buttons/cool_button/**/*',
        ],
    },
    'installable': True,
    'auto_install': False,
    'application': False,
    'license': 'LGPL-3',
}
