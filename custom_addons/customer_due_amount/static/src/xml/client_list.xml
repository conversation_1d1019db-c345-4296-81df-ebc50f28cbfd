<?xml version="1.0" encoding="UTF-8"?>
<templates id="template" xml:space="preserve">

    <!-- Extend the partner list to add Total Due column header -->
    <t t-name="customer_due_amount.PartnerList" t-inherit="point_of_sale.PartnerList" t-inherit-mode="extension">
        <xpath expr="//th[@class='partner-line-email py-2']" position="after">
            <th class="pos-right-align py-2">Total Due</th>
        </xpath>
    </t>

    <!-- Extend the partner line to add Total Due value -->
    <t t-name="customer_due_amount.PartnerLine" t-inherit="point_of_sale.PartnerLine" t-inherit-mode="extension">
        <xpath expr="//td[@class='partner-line-email ']" position="after">
            <td class="pos-right-align">
                <t t-if="props.partner.total_due">
                    <t t-esc="env.utils.formatCurrency(props.partner.total_due)" />
                </t>
                <t t-else="">
                    <t t-esc="env.utils.formatCurrency(0)" />
                </t>
            </td>
        </xpath>
    </t>

</templates>
