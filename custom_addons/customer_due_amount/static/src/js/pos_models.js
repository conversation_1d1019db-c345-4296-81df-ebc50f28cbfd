/** @odoo-module */

import { PosStore } from "@point_of_sale/app/store/pos_store";
import { PosOrder } from "@point_of_sale/app/models/pos_order";
import { patch } from "@web/core/utils/patch";

// Patch Order model to force invoice functionality
patch(PosOrder.prototype, {
    setup() {
        super.setup(...arguments);
        this.to_invoice = true;
    },
    
    is_to_invoice() {
        return true;
    },
    
    set_to_invoice(to_invoice) {
        // Always force to_invoice to be true
        this.to_invoice = true;
    },
    
    init_from_JSON(json) {
        super.init_from_JSON(json);
        this.to_invoice = true; // Force invoice to true when loading from JSON
    },
    
    export_as_JSON() {
        const json = super.export_as_JSON();
        json.to_invoice = true; // Always export with invoice = true
        return json;
    },
});

patch(PosStore.prototype, {
    /**
     * Update partner due amount after order validation
     * @param {Object} partner - Partner object to update
     */
    async updatePartnerDue(partner) {
        if (!partner || !partner.id) {
            return;
        }

        try {
            const result = await this.data.call("res.partner", "search_read", [
                [["id", "=", partner.id]],
                ["total_due"]
            ]);

            if (result && result.length > 0) {
                const updatedPartner = result[0];
                // Update the partner in the POS data
                const posPartner = this.models["res.partner"].get(partner.id);
                if (posPartner) {
                    posPartner.total_due = updatedPartner.total_due;
                }
            }
        } catch (error) {
            console.error("Error updating partner due amount:", error);
        }
    },
});
