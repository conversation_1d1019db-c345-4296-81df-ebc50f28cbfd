/** @odoo-module */

import { PaymentScreen } from "@point_of_sale/app/screens/payment_screen/payment_screen";
import { patch } from "@web/core/utils/patch";

patch(PaymentScreen.prototype, {
    setup() {
        super.setup(...arguments);
        // Force invoice to be checked when payment screen loads
        if (this.currentOrder) {
            this.currentOrder.set_to_invoice(true);
        }
    },

    toggleIsToInvoice() {
        // Override the toggle function to prevent unchecking
        this.currentOrder.set_to_invoice(true);
        // Force the checkbox to remain checked
        this.render();
    },

    /**
     * Override validateOrder to update partner due amount after validation
     */
    async validateOrder(isForceValidate) {
        const result = await super.validateOrder(isForceValidate);
        
        // Update partner due amount if order has a customer
        const order = this.pos.get_order();
        if (order && order.partner_id) {
            await this.pos.updatePartnerDue(order.partner_id);
        }
        
        return result;
    },
});
