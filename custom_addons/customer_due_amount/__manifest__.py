# -*- coding: utf-8 -*-
{
    'name': "Customer Due Amount",
    'summary': "Display customer due amount in partner form and Point of Sale with mandatory invoicing",
    'description': """
        This module adds functionality to display customer due amounts:
        - Shows total due amount in customer/partner form view
        - Displays due amounts in Point of Sale customer list
        - Updates due amounts after POS order validation
        - Calculates due amounts from unpaid invoices
        - Forces mandatory invoicing for all POS orders (invoice checkbox always checked and cannot be unchecked)
    """,
    'author': "Custom Development",
    'website': "https://www.example.com",
    'category': 'Point of Sale',
    'version': '********.0',
    'depends': ['base', 'account', 'point_of_sale'],
    'data': [
        'security/ir.model.access.csv',
        'views/res_partner.xml',
    ],
    'demo': [
        'data/demo_data.xml',
    ],
    'assets': {
        'point_of_sale._assets_pos': [
            'customer_due_amount/static/src/js/pos_models.js',
            'customer_due_amount/static/src/js/payment_screen.js',
            'customer_due_amount/static/src/xml/client_list.xml',
        ],
    },
    'installable': True,
    'auto_install': False,
    'application': False,
}
