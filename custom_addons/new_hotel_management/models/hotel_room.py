# -*- coding: utf-8 -*-
from odoo import fields, models

class HotelRoom(models.Model):
    _name = 'hotel.room'
    _description = 'Hotel Room'
    _inherit = ['mail.thread', 'mail.activity.mixin']

    image = fields.Image(string='Room Image')
    name = fields.Char(string='Room Number', required=True, tracking=True)
    room_type = fields.Selection([
        ('single', 'Single'),
        ('double', 'Double'),
        ('suite', 'Suite'),
    ], string='Room Type', required=True, tracking=True)
    floor = fields.Integer(string='Floor Number', required=True, tracking=True)
    capacity = fields.Integer(string='Capacity', default=1)
    price_per_night = fields.Float(string='Price per Night', required=True)
    state = fields.Selection([
        ('available', 'Available'),
        ('occupied', 'Occupied'),
    ], string='Status', default='available', tracking=True)
    active = fields.Boolean(default=True)
    note = fields.Text(string='Notes')
    amenities = fields.Many2many('hotel.amenity', string='Amenities',
        help='Amenities available in this room')
    
    booking_ids = fields.One2many('room.booking', 'room_id', string='Booking History')
    current_booking_id = fields.Many2one('room.booking', string='Current Booking', 
        domain="[('state', '=', 'checked_in'), ('room_id', '=', id)]",
        help='Currently active booking for this room')
