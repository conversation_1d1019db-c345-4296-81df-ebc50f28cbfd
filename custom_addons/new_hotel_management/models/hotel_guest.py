# -*- coding: utf-8 -*-
from odoo import fields, models

class HotelGuest(models.Model):
    _name = 'hotel.guest'
    _description = 'Hotel Guest'
    _inherit = ['mail.thread', 'mail.activity.mixin']
    
    name = fields.Char(string='Name', required=True, tracking=True)
    image = fields.Image(string='Photo')
    email = fields.Char(string='Email')
    phone = fields.Char(string='Phone')
    nationality = fields.Char(string='Nationality')
    address = fields.Text(string='Address')
    note = fields.Text(string='Notes')
    id_type = fields.Selection([
        ('passport', 'Passport'),
        ('national_id', 'National ID'),
    ], string='ID Type', required=True)
    id_number = fields.Char(string='ID Number', required=True)
    booking_ids = fields.One2many('room.booking', 'guest_id', string='Bookings')
    active = fields.Boolean(default=True)
