# -*- coding: utf-8 -*-
from odoo import api, fields, models, _

class HotelService(models.Model):
    _name = 'hotel.service'
    _description = 'Hotel Service'

    name = fields.Char(string='Service Name', required=True)
    price = fields.Float(string='Price', required=True)
    category = fields.Selection([
        ('food', 'Food & Beverage'),
        ('cleaning', 'Cleaning'),
        ('transport', 'Transport'),
        ('spa', 'Spa & Wellness'),
        ('other', 'Other'),
    ], string='Category', required=True)
    description = fields.Text(string='Description')
    active = fields.Boolean(default=True)
