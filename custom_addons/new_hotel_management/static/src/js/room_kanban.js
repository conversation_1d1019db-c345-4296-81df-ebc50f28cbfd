/** @odoo-module */

import { KanbanController } from "@web/views/kanban/kanban_controller";
import { KanbanRenderer } from "@web/views/kanban/kanban_renderer";
import { KanbanRecord } from "@web/views/kanban/kanban_record";
import { registry } from "@web/core/registry";
import { _t } from "@web/core/l10n/translation";

export class RoomKanbanController extends KanbanController {
    setup() {
        super.setup();
        this.displayName = _t("Room Status");
    }
}

export class RoomKanbanRecord extends KanbanRecord {
    getRecordClasses() {
        const classes = super.getRecordClasses();
        const record = this.props.record;
        
        // Add status-based classes
        if (record.data.state === 'available') {
            classes.push('bg-success-light');
        } else if (record.data.state === 'occupied') {
            classes.push('bg-danger-light');
        } else if (record.data.state === 'maintenance') {
            classes.push('bg-warning-light');
        }
        
        return classes;
    }
}

export class RoomKanbanRenderer extends Kanban<PERSON>enderer {
    setup() {
        super.setup();
        this.KanbanRecord = RoomKanbanRecord;
    }
}

// Register the custom kanban view
registry.category("views").add("room_kanban", {
    type: "kanban",
    Controller: RoomKanbanController,
    Renderer: RoomKanbanRenderer,
    Record: RoomKanbanRecord,
});
