/** @odoo-module */

import { CalendarController } from "@web/views/calendar/calendar_controller";
import { CalendarModel } from "@web/views/calendar/calendar_model";
import { CalendarRenderer } from "@web/views/calendar/calendar_renderer";
import { registry } from "@web/core/registry";
import { _t } from "@web/core/l10n/translation";

export class RoomBookingCalendarController extends CalendarController {
    setup() {
        super.setup();
        this.displayName = _t("Room Booking Calendar");
    }

    onClickToday() {
        super.onClickToday();
        this.env.services.notification.add(_t("Calendar updated to today"), {
            type: "info",
        });
    }
}

export class RoomBookingCalendarModel extends CalendarModel {
    setup() {
        super.setup();
        this.ranges = ["day", "week", "month"];
    }
}

export class RoomBookingCalendarRenderer extends CalendarRenderer {
    getEventColor(record) {
        // Color based on booking state
        const stateColors = {
            draft: "#9E9E9E",      // <PERSON>
            confirmed: "#4CAF50",   // <PERSON>
            checked_in: "#2196F3",  // Blue
            checked_out: "#795548", // <PERSON>
            cancelled: "#F44336",   // Red
        };
        return stateColors[record.state] || "#9E9E9E";
    }

    // Customize the event template
    getEventTemplate() {
        return "new_hotel_management.RoomBookingCalendarEvent";
    }
}

// Register the custom calendar view
registry.category("views").add("room_booking_calendar", {
    type: "calendar",
    Controller: RoomBookingCalendarController,
    Model: RoomBookingCalendarModel,
    Renderer: RoomBookingCalendarRenderer,
});
