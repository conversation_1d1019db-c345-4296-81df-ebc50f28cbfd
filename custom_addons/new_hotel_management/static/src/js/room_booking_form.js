/** @odoo-module */

import { FormController } from "@web/views/form/form_controller";
import { FormRenderer } from "@web/views/form/form_renderer";
import { registry } from "@web/core/registry";
import { _t } from "@web/core/l10n/translation";

export class RoomBookingFormController extends FormController {
    setup() {
        super.setup();
        this.displayName = _t("Room Booking");
    }

    async onRecordSaved(record) {
        await super.onRecordSaved(record);
        // Show notification after saving
        this.env.services.notification.add(
            _t("Booking saved successfully!"),
            { type: "success" }
        );
    }
}

export class RoomBookingFormRenderer extends FormRenderer {
    setup() {
        super.setup();
        this.state = {
            showServicePopup: false,
        };
    }

    // Calculate total nights when dates change
    async onDateChange() {
        const checkIn = this.props.record.data.check_in;
        const checkOut = this.props.record.data.check_out;
        
        if (checkIn && checkOut) {
            const diffTime = Math.abs(new Date(checkOut) - new Date(checkIn));
            const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
            await this.props.record.update({ total_nights: diffDays });
        }
    }
}

// Register the custom form view
registry.category("views").add("room_booking_form", {
    type: "form",
    Controller: RoomBookingFormController,
    Renderer: RoomBookingFormRenderer,
});
