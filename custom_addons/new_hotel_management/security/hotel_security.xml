<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="module_hotel_management" model="ir.module.category">
        <field name="name">Hotel Management</field>
        <field name="description">User access level for Hotel Management System</field>
        <field name="sequence">10</field>
    </record>

    <record id="group_hotel_user" model="res.groups">
        <field name="name">User</field>
        <field name="category_id" ref="module_hotel_management"/>
    </record>

    <record id="group_hotel_manager" model="res.groups">
        <field name="name">Manager</field>
        <field name="category_id" ref="module_hotel_management"/>
        <field name="implied_ids" eval="[(4, ref('group_hotel_user'))]"/>
    </record>
</odoo>
