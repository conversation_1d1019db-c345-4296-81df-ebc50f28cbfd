<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Tree View -->
    <record id="view_hotel_service_list" model="ir.ui.view">
        <field name="name">hotel.service.list</field>
        <field name="model">hotel.service</field>
        <field name="arch" type="xml">
            <list string="Services">
                <field name="name"/>
                <field name="category"/>
                <field name="price"/>
            </list>
        </field>
    </record>

    <!-- Form View -->
    <record id="view_hotel_service_form" model="ir.ui.view">
        <field name="name">hotel.service.form</field>
        <field name="model">hotel.service</field>
        <field name="arch" type="xml">
            <form string="Service">
                <sheet>
                    <div class="oe_title">
                        <h1>
                            <field name="name" placeholder="Service Name"/>
                        </h1>
                    </div>
                    <group>
                        <group>
                            <field name="category"/>
                            <field name="price"/>
                        </group>
                        <group>
                            <field name="active"/>
                        </group>
                    </group>
                    <notebook>
                        <page string="Description">
                            <field name="description"/>
                        </page>
                    </notebook>
                </sheet>
            </form>
        </field>
    </record>

    <!-- Search View -->
    <record id="view_hotel_service_search" model="ir.ui.view">
        <field name="name">hotel.service.search</field>
        <field name="model">hotel.service</field>
        <field name="arch" type="xml">
            <search string="Services">
                <field name="name"/>
                <field name="category"/>
                <separator/>
                <filter string="Food &amp; Beverage" name="food" domain="[('category', '=', 'food')]"/>
                <filter string="Cleaning" name="cleaning" domain="[('category', '=', 'cleaning')]"/>
                <filter string="Transport" name="transport" domain="[('category', '=', 'transport')]"/>
                <filter string="Spa &amp; Wellness" name="spa" domain="[('category', '=', 'spa')]"/>
                <group expand="0" string="Group By">
                    <filter string="Category" name="group_category" context="{'group_by': 'category'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- Action -->
    <record id="action_hotel_service" model="ir.actions.act_window">
        <field name="name">Services</field>
        <field name="res_model">hotel.service</field>
        <field name="view_mode">list,form</field>
        <field name="search_view_id" ref="view_hotel_service_search"/>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create your first service!
            </p>
        </field>
    </record>
</odoo>
