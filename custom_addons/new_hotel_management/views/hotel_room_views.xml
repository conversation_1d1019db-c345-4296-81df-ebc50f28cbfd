<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- List View -->
    <record id="view_hotel_room_list" model="ir.ui.view">
        <field name="name">hotel.room.list</field>
        <field name="model">hotel.room</field>
        <field name="arch" type="xml">
            <list string="Rooms">
                <field name="name"/>
                <field name="room_type"/>
                <field name="floor"/>
                <field name="capacity"/>
                <field name="price_per_night"/>
                <field name="state" decoration-success="state == 'available'" 
                       decoration-danger="state == 'occupied'" 
                       decoration-warning="state == 'maintenance'"/>
            </list>
        </field>
    </record>

    <!-- Form View -->
    <record id="view_hotel_room_form" model="ir.ui.view">
        <field name="name">hotel.room.form</field>
        <field name="model">hotel.room</field>
        <field name="arch" type="xml">
            <form string="Room">
                <header>
                    <field name="state" widget="statusbar"/>
                </header>
                <sheet>
                    <field name="image" widget="image" class="oe_avatar"/>
                    <div class="oe_title">
                        <h1>
                            <field name="name" placeholder="Room Number"/>
                        </h1>
                    </div>
                    <group>
                        <group>
                            <field name="room_type"/>
                            <field name="floor"/>
                            <field name="capacity"/>
                            <field name="price_per_night"/>
                        </group>
                        <group>
                            <field name="current_booking_id"/>
                            <field name="active"/>
                        </group>
                    </group>
                    <notebook>
                        <page string="Amenities">
                            <field name="amenities"/>
                        </page>
                        <page string="Booking History">
                            <field name="booking_ids" readonly="1">
                                <list>
                                    <field name="name"/>
                                    <field name="guest_id"/>
                                    <field name="check_in"/>
                                    <field name="check_out"/>
                                    <field name="total_amount"/>
                                    <field name="state"/>
                                </list>
                            </field>
                        </page>
                        <page string="Notes">
                            <field name="note"/>
                        </page>
                    </notebook>
                </sheet>
                <div class="oe_chatter">
                    <field name="message_follower_ids"/>
                    <field name="activity_ids"/>
                    <field name="message_ids"/>
                </div>
            </form>
        </field>
    </record>

    <!-- Search View -->
    <record id="view_hotel_room_search" model="ir.ui.view">
        <field name="name">hotel.room.search</field>
        <field name="model">hotel.room</field>
        <field name="arch" type="xml">
            <search string="Rooms">
                <field name="name"/>
                <field name="room_type"/>
                <field name="floor"/>
                <separator/>
                <filter string="Available" name="available" domain="[('state', '=', 'available')]"/>
                <filter string="Occupied" name="occupied" domain="[('state', '=', 'occupied')]"/>
                <filter string="Maintenance" name="maintenance" domain="[('state', '=', 'maintenance')]"/>
                <group expand="0" string="Group By">
                    <filter string="Room Type" name="group_room_type" context="{'group_by': 'room_type'}"/>
                    <filter string="Floor" name="group_floor" context="{'group_by': 'floor'}"/>
                    <filter string="Status" name="group_state" context="{'group_by': 'state'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- Action -->
    <record id="action_hotel_room" model="ir.actions.act_window">
        <field name="name">Rooms</field>
        <field name="res_model">hotel.room</field>
        <field name="view_mode">tree,form</field>
        <field name="search_view_id" ref="view_hotel_room_search"/>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create your first room!
            </p>
        </field>
    </record>
</odoo>
