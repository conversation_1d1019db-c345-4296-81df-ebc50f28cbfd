<?xml version="1.0" encoding="UTF-8"?>
<templates id="template" xml:space="preserve">

    <t t-name="hotel_pos_room_transfer.RoomTransferPopup">
        <Dialog size="'lg'">
            <t t-set-slot="title">
                <div class="title d-flex justify-content-between align-items-center">
                    <span>Transfer to Room</span>
                    <span class="order-total">Total: <t t-esc="env.utils.formatCurrency(props.orderTotal)"/></span>
                </div>
            </t>
            
            <div class="popup-body">
                <!-- Search Box -->
                <div class="search-box">
                    <input type="text" 
                           placeholder="Search rooms, guests, or phone numbers..." 
                           t-model="state.searchTerm"
                           t-on-input="onSearchInput"
                           class="search-input"/>
                    <i class="fa fa-search search-icon"/>
                </div>

                <!-- Rooms List -->
                <div class="rooms-list">
                    <div t-if="filteredRooms.length === 0" class="no-rooms">
                        <i class="fa fa-info-circle"/>
                        <span>No rooms found</span>
                    </div>
                    
                    <div t-else="">
                        <div t-foreach="filteredRooms" t-as="room" t-key="room.id"
                             class="room-item"
                             t-att-class="{'selected': state.selectedRoom and state.selectedRoom.id === room.id}"
                             t-on-click="() => this.selectRoom(room)">
                            
                            <div class="room-info">
                                <div class="room-header">
                                    <span class="room-number">Room <t t-esc="room.number"/></span>
                                    <span class="room-type"><t t-esc="room.room_type"/></span>
                                </div>
                                
                                <div class="guest-info">
                                    <i class="fa fa-user"/>
                                    <span class="guest-name"><t t-esc="room.guest_name"/></span>
                                </div>
                                
                                <div t-if="room.guest_phone" class="guest-contact">
                                    <i class="fa fa-phone"/>
                                    <span><t t-esc="room.guest_phone"/></span>
                                </div>
                                
                                <div class="room-charges">
                                    <span>Current Charges: </span>
                                    <span class="charges-amount">
                                        <t t-esc="env.utils.formatCurrency(room.total_charges)"/>
                                    </span>
                                </div>
                            </div>
                            
                            <div class="room-actions">
                                <i class="fa fa-check-circle" t-if="state.selectedRoom and state.selectedRoom.id === room.id"/>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Note Section -->
                <div class="note-section" t-if="state.selectedRoom">
                    <label>Note (Optional):</label>
                    <textarea placeholder="Add a note for this room transfer..."
                              t-model="state.note"
                              t-on-input="onNoteInput"
                              class="note-input"/>
                </div>
            </div>
            <t t-set-slot="footer">
                <button class="btn btn-secondary" t-on-click="onCancel">
                    Cancel
                </button>
                <button class="btn btn-primary" t-att-disabled="!state.selectedRoom" t-on-click="onConfirm">
                    Transfer
                </button>
            </t>
        </Dialog>
    </t>

</templates>