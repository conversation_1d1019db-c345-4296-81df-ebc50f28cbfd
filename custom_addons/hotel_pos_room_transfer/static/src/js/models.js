/** @odoo-module */

import { patch } from "@web/core/utils/patch";
import { Base } from "@point_of_sale/app/models/related_models";

// Create our own store class
export class HotelPosStore extends Base {
    setup() {
        super.setup();
        this.hotel_rooms = [];
        this.hotel_guests = [];
        this.occupied_rooms = [];
    }
}

console.log("HotelPOS: models.js (v1.1) - Module loaded.");

// Patch HotelPosStore to load hotel data
patch(HotelPosStore.prototype, {
    setup() {
        super.setup(...arguments);
        // Initialize hotel-related data
        this.hotel_rooms = [];
        this.hotel_guests = [];
        this.occupied_rooms = [];
        // Get rpc service
        this.rpc = this.env.services.rpc;
    },

    async _processData(loadedData) {
        console.log('HotelPOS: PosStore._processData - Starting.');
        await super._processData(...arguments);
        
        console.log('HotelPOS: PosStore._processData - Hotel data from loadedData:', 
            { rooms: loadedData['hotel.room'], guests: loadedData['hotel.guest'] });
        // Load hotel rooms and guests
        this.hotel_rooms = loadedData['hotel.room'] || [];
        this.hotel_guests = loadedData['hotel.guest'] || [];
        console.log('HotelPOS: PosStore._processData - Hotel data loaded into this.hotel_rooms and this.hotel_guests.');
    },

    //Get occupied rooms with guests
    async loadOccupiedRooms() {
        console.log('HotelPOS: PosStore.loadOccupiedRooms - Attempting to load occupied rooms.');
        try {
            const rooms = await this.rpc('/hotel_pos_room_transfer/get_occupied_rooms');
            console.log('HotelPOS: PosStore.loadOccupiedRooms - Successfully loaded rooms:', { count: rooms.length, rooms });
            this.occupied_rooms = rooms;
            return rooms;
        } catch (error) {
            console.error('HotelPOS: PosStore.loadOccupiedRooms - Error loading occupied rooms:', error);
            this.occupied_rooms = []; // Ensure it's an empty array on error
            return [];
        }
    },
});
console.log("HotelPOS: PosStore patched successfully.");

// Create our own order class
export class HotelPosOrder extends Base {
    setup() {
        super.setup();
        this.room_id = null;
        this.guest_id = null;
        this.is_room_transfer = false;
        this.room_transfer_note = '';
    }
}

// Patch HotelPosOrder to add room transfer functionality
patch(HotelPosOrder.prototype, {
    setup() {
        super.setup(...arguments);
        this.room_id = this.room_id || null;
        this.guest_id = this.guest_id || null;
        this.is_room_transfer = this.is_room_transfer || false;
        this.room_transfer_note = this.room_transfer_note || '';
        // console.log('HotelPOS: Order.setup - Initialized room transfer properties for order:', this.name);
    },

    init_from_JSON(json) {
        super.init_from_JSON(...arguments);
        this.room_id = json.room_id || null;
        this.guest_id = json.guest_id || null;
        this.is_room_transfer = json.is_room_transfer || false;
        this.room_transfer_note = json.room_transfer_note || '';
        console.log('HotelPOS: Order.init_from_JSON - Loaded room transfer properties for order:', { name: this.name, json });
    },

    export_as_JSON() {
        const json = super.export_as_JSON(...arguments);
        json.room_id = this.room_id;
        json.guest_id = this.guest_id;
        json.is_room_transfer = this.is_room_transfer;
        json.room_transfer_note = this.room_transfer_note;
        // console.log('HotelPOS: Order.export_as_JSON - Exported room transfer properties for order:', this.name);
        return json;
    },

    setRoomTransfer(room, guest, note = '') {
        console.log('HotelPOS: Order.setRoomTransfer - Setting room transfer for order:', { orderName: this.name, roomId: room.id, guestId: guest.id, note });
        this.room_id = room.id;
        this.guest_id = guest.id;
        this.is_room_transfer = true;
        this.room_transfer_note = note;
        
        // Set the customer to the guest's partner
        // Assuming guest object has partner_id and pos has partners loaded
        const guestPartnerId = room.guest_partner_id || guest.partner_id; // Prefer guest_partner_id from room data if available
        if (guestPartnerId) {
            const partner = this.pos.db.get_partner_by_id(guestPartnerId);
            if (partner) {
                console.log('HotelPOS: Order.setRoomTransfer - Setting partner for order:', partner.name);
                this.set_partner(partner);
            } else {
                console.warn('HotelPOS: Order.setRoomTransfer - Partner not found in POS for guest_partner_id:', guestPartnerId);
            }
        } else {
            console.warn('HotelPOS: Order.setRoomTransfer - No partner_id found for guest.');
        }
    },

    getRoomInfo() {
        if (!this.room_id) return null;
        const roomInfo = this.pos.occupied_rooms?.find(room => room.id === this.room_id) || null;
        // console.log('HotelPOS: Order.getRoomInfo - Room info for order:', { orderName: this.name, roomInfo });
        return roomInfo;
    },

    getGuestInfo() {
        if (!this.guest_id) return null;
        // Guest info is part of the room object in occupied_rooms
        const roomWithGuest = this.pos.occupied_rooms?.find(room => room.guest_id === this.guest_id);
        const guestInfo = roomWithGuest ? { id: roomWithGuest.guest_id, name: roomWithGuest.guest_name, phone: roomWithGuest.guest_phone, email: roomWithGuest.guest_email } : null;
        // console.log('HotelPOS: Order.getGuestInfo - Guest info for order:', { orderName: this.name, guestInfo });
        return guestInfo;
    },
});
console.log("HotelPOS: Order patched successfully.");