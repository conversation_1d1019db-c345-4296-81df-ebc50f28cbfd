/** @odoo-module */

import { Component } from "@odoo/owl";
import { patch } from "@web/core/utils/patch";
import { useService } from "@web/core/utils/hooks";
import { _t } from "@web/core/l10n/translation";
import { Dialog } from "@web/core/dialog/dialog";
import { HotelPosComponent } from "./base_component";
import { RoomTransferPopup } from "./room_transfer_popup";

import { PaymentScreen } from "@point_of_sale/app/screens/payment_screen/payment_screen";

patch(PaymentScreen.prototype, {
    setup() {
        super.setup(...arguments);
        this.dialog = useService("dialog");
        this.pos = this.env.pos;
        console.log("HotelPOS: PaymentScreen.setup - Patched setup complete. Services:", { dialog: this.dialog, pos: this.pos });
    },

    async transferToRoom() {
        console.log("HotelPOS: PaymentScreen.transferToRoom - 'Transfer to Room' button clicked.");
        // Check if room transfer is enabled
        if (!this.pos.config.enable_room_transfer) {
            console.warn("HotelPOS: PaymentScreen.transferToRoom - Room transfer is not enabled in POS config.");
            await this.dialog.add(Dialog, {
                title: _t("Feature Disabled"),
                body: _t("Room transfer is not enabled in POS configuration."),
            });
            return;
        }
        console.log("HotelPOS: PaymentScreen.transferToRoom - Room transfer is enabled. Current order:", this.currentOrder.name);

        // Load occupied rooms
        console.log("HotelPOS: PaymentScreen.transferToRoom - Loading occupied rooms...");
        await this.pos.loadOccupiedRooms();
        
        if (!this.pos.occupied_rooms || this.pos.occupied_rooms.length === 0) {
            console.warn("HotelPOS: PaymentScreen.transferToRoom - No occupied rooms available.");
            await this.dialog.add(Dialog, {
                title: _t("No Occupied Rooms"),
                body: _t("There are no occupied rooms available for transfer."),
            });
            return;
        }
        console.log("HotelPOS: PaymentScreen.transferToRoom - Occupied rooms loaded:", this.pos.occupied_rooms);

        // Show room transfer popup
        console.log("HotelPOS: PaymentScreen.transferToRoom - Showing RoomTransferPopup.");
        const result = await new Promise((resolve) => {
            this.dialog.add(RoomTransferPopup, {
                title: _t("Transfer to Room"),
                rooms: this.pos.occupied_rooms,
                orderTotal: this.currentOrder.get_total_with_tax(),
                onConfirm: (payload) => resolve({ confirmed: true, payload }),
                onCancel: () => resolve({ confirmed: false }),
            });
        });
        console.log("HotelPOS: PaymentScreen.transferToRoom - RoomTransferPopup result:", result);

        if (result.confirmed && result.payload) {
            console.log("HotelPOS: PaymentScreen.transferToRoom - Popup confirmed. Processing room transfer with payload:", result.payload);
            await this.processRoomTransfer(result.payload);
        } else {
            console.log("HotelPOS: PaymentScreen.transferToRoom - Popup cancelled or no payload.");
        }
    },

    async processRoomTransfer(payload) {
        const { room, note } = payload;
        console.log("HotelPOS: PaymentScreen.processRoomTransfer - Starting process for room:", room, "Note:", note);
        
        try {
            // Set room transfer on current order
            console.log("HotelPOS: PaymentScreen.processRoomTransfer - Setting room transfer on current order.");
            // The guest object needs to be structured correctly for setRoomTransfer
            // The room object from get_occupied_rooms_with_guests contains guest_id, guest_name, etc.
            // It also needs partner_id for setting the customer on the order.
            // Let's assume hotel_room.get_occupied_rooms_with_guests includes guest_partner_id
            const guestData = { 
                id: room.guest_id, 
                partner_id: room.guest_partner_id, // This field needs to be added to the get_occupied_rooms_with_guests payload
                name: room.guest_name 
            };
            this.currentOrder.setRoomTransfer(room, guestData, note);

            // Add room transfer payment method
            const roomPaymentMethodId = this.pos.config.room_transfer_payment_method_id;
            if (roomPaymentMethodId && roomPaymentMethodId[0]) {
                const paymentMethod = this.pos.payment_methods_by_id[roomPaymentMethodId[0]];
                
                if (paymentMethod) {
                    console.log("HotelPOS: PaymentScreen.processRoomTransfer - Found room transfer payment method:", paymentMethod.name);
                    // Remove existing payments
                    this.currentOrder.paymentlines.forEach(line => {
                        console.log("HotelPOS: PaymentScreen.processRoomTransfer - Removing existing payment line:", line.payment_method.name);
                        this.currentOrder.remove_paymentline(line);
                    });
                    
                    // Add room transfer payment
                    console.log("HotelPOS: PaymentScreen.processRoomTransfer - Adding room transfer payment line.");
                    this.currentOrder.add_paymentline(paymentMethod);
                    const paymentLine = this.currentOrder.selected_paymentline;
                    paymentLine.set_amount(this.currentOrder.get_due());
                    paymentLine.set_payment_status('done'); // Mark as done as it's a transfer
                    console.log("HotelPOS: PaymentScreen.processRoomTransfer - Room transfer payment line added and amount set.");
                } else {
                     console.warn("HotelPOS: PaymentScreen.processRoomTransfer - Room transfer payment method (ID:", roomPaymentMethodId[0], ") not found in pos.payment_methods_by_id.");
                }
            } else {
                console.warn("HotelPOS: PaymentScreen.processRoomTransfer - Room transfer payment method ID not configured in POS settings.");
            }

            // Show success message
            console.log("HotelPOS: PaymentScreen.processRoomTransfer - Transfer successful. Showing confirmation.");
            await this.popup.add("ConfirmPopup", {
                title: _t("Transfer Successful"),
                body: _t(`Order transferred to Room ${room.number} for ${room.guest_name}`),
            });

            // Validate the order
            await this.validateOrder(true);
            console.log("HotelPOS: PaymentScreen.processRoomTransfer - Order validated.");

        } catch (error) {
            console.error('HotelPOS: PaymentScreen.processRoomTransfer - Room transfer error:', error);
            await this.dialog.add(Dialog, {
                title: _t("Transfer Failed"),
                body: _t("Failed to transfer order to room. Check console for details. Error: " + error.message),
            });
        }
    },
});
console.log("HotelPOS: PaymentScreen patched successfully.");

// Ensure the `registry` is available for popups
import { registry } from "@web/core/registry";
if (!registry.category("pos_popups").get("RoomTransferPopup", { silent: true })) {
    console.warn("HotelPOS: RoomTransferPopup not found in pos_popups registry. It should be registered by room_transfer_popup.js.");
}