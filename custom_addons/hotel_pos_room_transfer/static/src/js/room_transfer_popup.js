/** @odoo-module */

import { useState } from "@odoo/owl";
import { _t } from "@web/core/l10n/translation";
import { Dialog } from "@web/core/dialog/dialog";
import { HotelPosComponent } from "./base_component";

console.log("HotelPOS: room_transfer_popup.js (v1.1) - Module loaded.");
export class RoomTransferPopup extends HotelPosComponent {
    static template = "hotel_pos_room_transfer.RoomTransferPopup";
    static template = "hotel_pos_room_transfer.RoomTransferPopup";
    static props = {
        rooms: { type: Array },
        orderTotal: { type: Number },
        onConfirm: { type: Function },
        onCancel: { type: Function },
    };

    setup() {
        super.setup();
        console.log('HotelPOS: RoomTransferPopup.setup - Initializing.', { 
            props: this.props,
            roomsCount: this.props.rooms?.length, 
            orderTotal: this.props.orderTotal,
            currentOrderName: this.props.currentOrder?.name 
        });
        this.state = useState({
            selectedRoom: null,
            searchTerm: '',
            note: '',
        });
    }

    get filteredRooms() {
        if (!this.props.rooms) { console.log('HotelPOS: RoomTransferPopup.filteredRooms - No rooms in props.'); return [];}
        
        if (!this.state.searchTerm) {
            // console.log('HotelPOS: RoomTransferPopup.filteredRooms - Displaying all rooms', { count: this.props.rooms.length });
            return this.props.rooms;
        }
        
        const searchTerm = this.state.searchTerm.toLowerCase();
        const filteredRooms = this.props.rooms.filter(room =>
            room.number.toLowerCase().includes(searchTerm) ||
            room.guest_name.toLowerCase().includes(searchTerm) ||
            (room.guest_phone && room.guest_phone.toLowerCase().includes(searchTerm))
        );
        // console.log('HotelPOS: RoomTransferPopup.filteredRooms - Filtering rooms', { searchTerm, filteredCount: filteredRooms.length });
        return filteredRooms;
    }

    selectRoom(room) {
        console.log('HotelPOS: RoomTransferPopup.selectRoom - Selected room:', { roomId: room.id, roomNumber: room.number, guestName: room.guest_name });
        this.state.selectedRoom = room;
    }

    onSearchInput(event) {
        this.state.searchTerm = event.target.value;
    }

    onNoteInput(event) {
        this.state.note = event.target.value;
    }

    onConfirm() {
        if (!this.state.selectedRoom) {
            console.warn('HotelPOS: RoomTransferPopup.confirm - No room selected.');
            return;
        }
        console.log('HotelPOS: RoomTransferPopup.confirm - Confirming transfer to room:', { 
            room: this.state.selectedRoom, 
            note: this.state.note 
        });
        this.props.onConfirm({ 
            room: this.state.selectedRoom,
            note: this.state.note,
        });
        this.props.close();
    }
}

console.log("HotelPOS: RoomTransferPopup component defined.");