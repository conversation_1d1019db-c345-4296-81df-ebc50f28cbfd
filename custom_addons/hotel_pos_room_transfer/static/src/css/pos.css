/* Room Transfer Button Styles */
.btn-transfer-room {
    background-color: #17a2b8 !important;
    border-color: #17a2b8 !important;
    color: white !important;
    margin: 5px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-transfer-room:hover {
    background-color: #138496 !important;
    border-color: #117a8b !important;
    transform: translateY(-1px);
}

.btn-transfer-room i {
    margin-right: 8px;
}

/* Room Transfer Popup Styles */
.room-transfer-popup {
    width: 600px;
    max-height: 80vh;
}

.room-transfer-popup .popup-header {
    padding: 20px;
    border-bottom: 1px solid #dee2e6;
    background-color: #f8f9fa;
}

.room-transfer-popup .title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 18px;
    font-weight: 600;
}

.room-transfer-popup .order-total {
    color: #28a745;
    font-weight: 700;
}

.room-transfer-popup .popup-body {
    padding: 20px;
    max-height: 50vh;
    overflow-y: auto;
}

/* Search Box Styles */
.search-box {
    position: relative;
    margin-bottom: 20px;
}

.search-input {
    width: 100%;
    padding: 12px 45px 12px 15px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 14px;
    transition: border-color 0.3s ease;
}

.search-input:focus {
    outline: none;
    border-color: #17a2b8;
}

.search-icon {
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: #6c757d;
}

/* Rooms List Styles */
.rooms-list {
    max-height: 300px;
    overflow-y: auto;
}

.no-rooms {
    text-align: center;
    padding: 40px 20px;
    color: #6c757d;
}

.no-rooms i {
    font-size: 24px;
    margin-bottom: 10px;
    display: block;
}

.room-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    margin-bottom: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.room-item:hover {
    border-color: #17a2b8;
    background-color: #f8f9fa;
}

.room-item.selected {
    border-color: #28a745;
    background-color: #d4edda;
}

.room-info {
    flex: 1;
}

.room-header {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
}

.room-number {
    font-weight: 600;
    font-size: 16px;
    color: #495057;
    margin-right: 10px;
}

.room-type {
    background-color: #6c757d;
    color: white;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 12px;
    text-transform: uppercase;
}

.guest-info, .guest-contact {
    display: flex;
    align-items: center;
    margin-bottom: 5px;
    color: #6c757d;
    font-size: 14px;
}

.guest-info i, .guest-contact i {
    width: 16px;
    margin-right: 8px;
}

.guest-name {
    font-weight: 500;
    color: #495057;
}

.room-charges {
    font-size: 14px;
    color: #6c757d;
}

.charges-amount {
    font-weight: 600;
    color: #dc3545;
}

.room-actions {
    display: flex;
    align-items: center;
}

.room-actions .fa-check-circle {
    color: #28a745;
    font-size: 20px;
}

/* Note Section Styles */
.note-section {
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid #dee2e6;
}

.note-section label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #495057;
}

.note-input {
    width: 100%;
    padding: 10px;
    border: 2px solid #e9ecef;
    border-radius: 6px;
    font-size: 14px;
    min-height: 80px;
    resize: vertical;
    transition: border-color 0.3s ease;
}

.note-input:focus {
    outline: none;
    border-color: #17a2b8;
}

/* Responsive Design */
@media (max-width: 768px) {
    .room-transfer-popup {
        width: 95vw;
        max-width: 500px;
    }
    
    .room-item {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .room-actions {
        align-self: flex-end;
        margin-top: 10px;
    }
}