{
    'name': 'Hotel POS Room Transfer',
    'version': '1.0',
    'category': 'Point of Sale',
    'summary': 'Room transfer functionality for hotel POS',
    'depends': ['point_of_sale', 'base'],  # Core dependencies
    'sequence': 10,  # Ensure it loads after the hotel module
    'auto_install': False,
    'controllers': [
        'controllers/main.py',
    ],
    'data': [
        'data/payment_method_data.xml',
        'views/hotel_room_views.xml',
        'views/hotel_guest_views.xml',
        'security/ir.model.access.csv',
        'views/pos_config_views.xml',
    ],
    'assets': {
        'point_of_sale._assets_pos': [
            'hotel_pos_room_transfer/static/src/css/pos.css',
            'hotel_pos_room_transfer/static/src/js/base_component.js',
            'hotel_pos_room_transfer/static/src/js/services.js',
            'hotel_pos_room_transfer/static/src/js/models.js',
            'hotel_pos_room_transfer/static/src/js/room_transfer_popup.js',
            'hotel_pos_room_transfer/static/src/js/payment_screen.js',
            'hotel_pos_room_transfer/static/src/xml/payment_screen.xml',
            'hotel_pos_room_transfer/static/src/xml/room_transfer_popup.xml',
        ],
    },
    'installable': True,
}