from odoo import models, fields

class PosConfig(models.Model):
    _inherit = 'pos.config'

    enable_room_transfer = fields.Boolean(
        string='Enable Room Transfer',
        default=False,
        help='Allow transfer of POS orders to hotel rooms.'
    )
    room_transfer_payment_method_id = fields.Many2one(
        'pos.payment.method',
        string='Room Transfer Payment Method',
        help='Payment method used for room transfers'
    )
    room_transfer_journal_id = fields.Many2one(
        'account.journal',
        string='Room Transfer Journal',
        help='Journal for room transfer accounting entries'
    )