from odoo import models, fields, api
import logging
from datetime import datetime
from dateutil.relativedelta import relativedelta

_logger = logging.getLogger(__name__)


class HotelRoom(models.Model):
    _name = 'hotel.room'
    _description = 'Hotel Room'
    _order = 'number'

    name = fields.Char(string='Room Name', required=True)
    number = fields.Char(string='Room Number', required=True)
    floor = fields.Char(string='Floor')
    room_type = fields.Selection([
        ('single', 'Single'),
        ('double', 'Double'),
        ('suite', 'Suite'),
        ('deluxe', 'Deluxe'),
    ], string='Room Type', default='single')
    status = fields.Selection([
        ('available', 'Available'),
        ('occupied', 'Occupied'),
        ('maintenance', 'Maintenance'),
        ('cleaning', 'Cleaning'),
    ], string='Status', default='available')
    guest_id = fields.Many2one('hotel.guest', string='Current Guest')
    pos_order_ids = fields.One2many('pos.order', 'room_id', string='POS Orders')
    total_room_charges = fields.Float(string='Total Room Charges', compute='_compute_total_charges', store=True)
    company_id = fields.Many2one('res.company', string='Company', default=lambda self: self.env.company)
    active = fields.Boolean(default=True)
    last_order_date = fields.Datetime(string='Last Order Date', readonly=True)
    last_order_amount = fields.Float(string='Last Order Amount', readonly=True)

    @api.depends('pos_order_ids.amount_total')
    def _compute_total_charges(self):
        for room in self:
            room.total_room_charges = sum(room.pos_order_ids.filtered(lambda o: o.state != 'cancel').mapped('amount_total'))

    @api.model
    def get_occupied_rooms_with_guests(self):
        """Get all occupied rooms with guest information for POS"""
        _logger.info("HotelPOS: get_occupied_rooms_with_guests called from POS.")
        rooms = self.search([('status', '=', 'occupied'), ('guest_id', '!=', False)])
        result = []
        for room in rooms:
            result.append({
                'id': room.id,
                'name': room.name,
                'number': room.number,
                'floor': room.floor,
                'room_type': room.room_type,
                'guest_id': room.guest_id.id,
                'guest_name': room.guest_id.name,
                'guest_phone': room.guest_id.phone or '',
                'guest_email': room.guest_id.email or '',
                'guest_partner_id': room.guest_id.partner_id.id if room.guest_id.partner_id else False,
                'total_charges': room.total_room_charges,
            })
        _logger.info(f"HotelPOS: Found {len(result)} occupied rooms with guests: {result}")
        return result

    def transfer_to_room_payment(self, order_data):
        """Process the room transfer payment"""
        _logger.info(f"HotelPOS: transfer_to_room_payment called for room {self.name} with order_data: {order_data}")
        if self.status != 'occupied' or not self.guest_id:
            _logger.warning(f"HotelPOS: Room {self.name} is not occupied or no guest assigned. Status: {self.status}, Guest: {self.guest_id.name if self.guest_id else 'None'}")
            return {'success': False, 'message': 'Room is not occupied or no guest assigned'}
        
        self.last_order_date = datetime.now()
        self.last_order_amount = order_data['amount']
        _logger.info(f"HotelPOS: Order transferred to room {self.name} successfully (server-side check).")
        return {'success': True, 'message': 'Order transferred to room successfully'}