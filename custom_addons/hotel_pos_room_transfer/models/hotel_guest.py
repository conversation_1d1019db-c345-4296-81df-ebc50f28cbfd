from odoo import models, fields, api
import logging

_logger = logging.getLogger(__name__)


class HotelGuest(models.Model):
    _name = 'hotel.guest'
    _description = 'Hotel Guest'
    _inherit = ['mail.thread', 'mail.activity.mixin']

    name = fields.Char(string='Guest Name', required=True, tracking=True)
    phone = fields.Char(string='Phone', tracking=True)
    email = fields.Char(string='Email', tracking=True)
    id_number = fields.Char(string='ID Number')
    id_type = fields.Selection([
        ('passport', 'Passport'),
        ('license', 'Driver License'),
        ('national_id', 'National ID'),
        ('other', 'Other'),
    ], string='ID Type', default='passport')
    check_in_date = fields.Datetime(string='Check-in Date', tracking=True)
    check_out_date = fields.Datetime(string='Check-out Date', tracking=True)
    room_ids = fields.One2many('hotel.room', 'guest_id', string='Rooms')
    pos_order_ids = fields.One2many('pos.order', 'guest_id', string='Restaurant Orders')
    partner_id = fields.Many2one('res.partner', string='Related Contact')
    company_id = fields.Many2one('res.company', string='Company', default=lambda self: self.env.company)
    active = fields.Boolean(default=True)
    total_charges = fields.Float(string='Total Charges', compute='_compute_total_charges', store=True)
    notes = fields.Text(string='Notes')

    @api.depends('pos_order_ids.amount_total')
    def _compute_total_charges(self):
        for guest in self:
            guest.total_charges = sum(guest.pos_order_ids.filtered(lambda o: o.state != 'cancel').mapped('amount_total'))

    @api.model
    def create(self, vals):
        """Create related partner when creating guest"""
        guest = super().create(vals)
        if not guest.partner_id:
            partner_vals = {
                'name': guest.name,
                'phone': guest.phone,
                'email': guest.email,
                'is_company': False,
                'supplier_rank': 0,
                'customer_rank': 1,
            }
            partner = self.env['res.partner'].create(partner_vals)
            guest.partner_id = partner.id
        return guest

    def write(self, vals):
        """Update related partner when updating guest"""
        result = super().write(vals)
        for guest in self:
            if guest.partner_id:
                partner_vals = {}
                if 'name' in vals:
                    partner_vals['name'] = vals['name']
                if 'phone' in vals:
                    partner_vals['phone'] = vals['phone']
                if 'email' in vals:
                    partner_vals['email'] = vals['email']
                if partner_vals:
                    guest.partner_id.write(partner_vals)
        return result

    def action_checkout(self):
        """Process guest checkout"""
        # Update room status
        for room in self.room_ids:
            room.write({
                'status': 'cleaning',
                'guest_id': False,
            })
        
        # Update guest status
        self.write({
            'check_out_date': fields.Datetime.now(),
            'active': False,
        })
        
        return True