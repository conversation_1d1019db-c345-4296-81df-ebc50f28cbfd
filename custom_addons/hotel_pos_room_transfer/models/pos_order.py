from odoo import models, fields, api
import logging

_logger = logging.getLogger(__name__)


class PosOrder(models.Model):
    _inherit = 'pos.order'

    room_id = fields.Many2one('hotel.room', string='Hotel Room', ondelete='set null')
    guest_id = fields.Many2one('hotel.guest', string='Hotel Guest', ondelete='set null')
    is_room_transfer = fields.Boolean(string='Room Transfer', default=False)
    room_transfer_note = fields.Text(string='Room Transfer Note')

    @api.model
    def _order_fields(self, ui_order):
        """Add room transfer fields to order creation"""
        _logger.info(f"HotelPOS: _order_fields called for ui_order: {ui_order.get('name', 'N/A')}, Room ID: {ui_order.get('room_id')}")
        order_fields = super()._order_fields(ui_order)
        order_fields.update({
            'room_id': ui_order.get('room_id', False),
            'guest_id': ui_order.get('guest_id', False),
            'is_room_transfer': ui_order.get('is_room_transfer', False),
            'room_transfer_note': ui_order.get('room_transfer_note', ''),
        })
        _logger.info(f"HotelPOS: Updated order_fields with room transfer data: {order_fields}")
        return order_fields

    def _get_fields_for_draft_order(self):
        """Include room transfer fields in draft orders"""
        fields = super()._get_fields_for_draft_order()
        fields.extend(['room_id', 'guest_id', 'is_room_transfer', 'room_transfer_note'])
        return fields

    @api.model
    def create_room_transfer_order(self, order_data):
        """Create order with room transfer"""
        _logger.info(f"HotelPOS: create_room_transfer_order called with data: {order_data}")
        try:
            # Validate room and guest
            room = self.env['hotel.room'].browse(order_data.get('room_id'))
            if not room.exists() or room.status != 'occupied':
                _logger.warning(f"HotelPOS: Invalid room or room not occupied. Room ID: {order_data.get('room_id')}, Status: {room.status if room.exists() else 'N/A'}")
                return {'success': False, 'message': 'Invalid room or room not occupied'}

            guest = self.env['hotel.guest'].browse(order_data.get('guest_id'))
            if not guest.exists():
                _logger.warning(f"HotelPOS: Invalid guest. Guest ID: {order_data.get('guest_id')}")
                return {'success': False, 'message': 'Invalid guest'}
            _logger.info(f"HotelPOS: Creating order for Room: {room.name}, Guest: {guest.name}, Partner: {guest.partner_id.name if guest.partner_id else 'No Partner'}")
            # Create the order
            order = self.create({
                'session_id': order_data['session_id'],
                'partner_id': guest.partner_id.id,
                'room_id': room.id,
                'guest_id': guest.id,
                'is_room_transfer': True,
                'room_transfer_note': order_data.get('note', ''),
                'lines': order_data.get('lines', []),
                'amount_total': order_data.get('amount_total', 0),
                'amount_tax': order_data.get('amount_tax', 0),
                'amount_paid': order_data.get('amount_total', 0),  # Mark as paid since it's transferred
                'state': 'paid',
            })

            _logger.info(f"HotelPOS: Room transfer order created successfully. Order ID: {order.id}, POS Ref: {order.pos_reference}")
            return {'success': True, 'order_id': order.id}
        except Exception as e:
            _logger.error(f"HotelPOS: Error creating room transfer order: {e}", exc_info=True)
            return {'success': False, 'message': str(e)}