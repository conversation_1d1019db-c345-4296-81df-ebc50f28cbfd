from odoo import http
from odoo.http import request

class HotelPosRoomTransferController(http.Controller):
    @http.route('/hotel_pos_room_transfer/get_occupied_rooms', type='json', auth='user')
    def get_occupied_rooms(self):
        """Get list of occupied rooms with guest information."""
        try:
            # Get the hotel.room model
            HotelRoom = request.env['hotel.room']
            # Call the model method to get occupied rooms
            rooms = HotelRoom.get_occupied_rooms_with_guests()
            return rooms
        except Exception as e:
            return {'error': str(e)}
