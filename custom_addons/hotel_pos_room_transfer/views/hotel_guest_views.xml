<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Hotel Guest List View -->
    <record id="view_hotel_guest_tree" model="ir.ui.view">
        <field name="name">hotel.guest.tree</field>
        <field name="model">hotel.guest</field>
        <field name="arch" type="xml">
            <list string="Hotel Guests">
                <field name="name"/>
                <field name="phone"/>
                <field name="email"/>
                <field name="check_in_date"/>
                <field name="check_out_date"/>
                <field name="total_charges" widget="monetary"/>
                <field name="active" invisible="1"/>
            </list>
        </field>
    </record>

    <!-- Hotel Guest Form View -->
    <record id="view_hotel_guest_form" model="ir.ui.view">
        <field name="name">hotel.guest.form</field>
        <field name="model">hotel.guest</field>
        <field name="arch" type="xml">
            <form string="Hotel Guest">
                <header>
                    <button name="action_checkout" string="Checkout" type="object" class="oe_highlight" 
                            invisible="check_out_date != False"/>
                </header>
                <sheet>
                    <group>
                        <group>
                            <field name="name"/>
                            <field name="phone"/>
                            <field name="email"/>
                        </group>
                        <group>
                            <field name="id_type"/>
                            <field name="id_number"/>
                            <field name="check_in_date"/>
                            <field name="check_out_date"/>
                            <field name="total_charges" widget="monetary"/>
                        </group>
                    </group>
                    <group>
                        <field name="notes" placeholder="Additional notes about the guest..."/>
                    </group>
                    <notebook>
                        <page string="Rooms">
                            <field name="room_ids">
                                <list>
                                    <field name="number"/>
                                    <field name="name"/>
                                    <field name="status"/>
                                    <field name="total_room_charges" widget="monetary"/>
                                </list>
                            </field>
                        </page>
                        <page string="Restaurant Orders">
                            <field name="pos_order_ids" readonly="1">
                                <list>
                                    <field name="name"/>
                                    <field name="date_order"/>
                                    <field name="amount_total" widget="monetary"/>
                                    <field name="state"/>
                                    <field name="is_room_transfer"/>
                                </list>
                            </field>
                        </page>
                    </notebook>
                </sheet>
                <div class="oe_chatter">
                    <field name="message_follower_ids"/>
                    <field name="activity_ids"/>
                    <field name="message_ids"/>
                </div>
            </form>
        </field>
    </record>

    <!-- Hotel Guest Action -->
    <record id="action_hotel_guest" model="ir.actions.act_window">
        <field name="name">Hotel Guests</field>
        <field name="res_model">hotel.guest</field>
        <field name="view_mode">list,form</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Register your first hotel guest!
            </p>
            <p>
                Manage hotel guests, their room assignments, and track charges.
            </p>
        </field>
    </record>

    <!-- Menu Item -->
    <menuitem id="menu_hotel_guests" name="Guests" parent="point_of_sale.menu_point_root" action="action_hotel_guest" sequence="20"/>
</odoo>