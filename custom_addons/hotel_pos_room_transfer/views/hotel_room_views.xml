<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Hotel Room List View -->
    <record id="view_hotel_room_tree" model="ir.ui.view">
        <field name="name">hotel.room.tree</field>
        <field name="model">hotel.room</field>
        <field name="arch" type="xml">
            <list string="Hotel Rooms">
                <field name="number"/>
                <field name="name"/>
                <field name="floor"/>
                <field name="room_type"/>
                <field name="status"/>
                <field name="guest_id"/>
                <field name="total_room_charges" widget="monetary"/>
            </list>
        </field>
    </record>

    <!-- Hotel Room Form View -->
    <record id="view_hotel_room_form" model="ir.ui.view">
        <field name="name">hotel.room.form</field>
        <field name="model">hotel.room</field>
        <field name="arch" type="xml">
            <form string="Hotel Room">
                <header>
                    <field name="status" widget="statusbar" options="{'clickable': '1'}"/>
                </header>
                <sheet>
                    <group>
                        <group>
                            <field name="number"/>
                            <field name="name"/>
                            <field name="floor"/>
                            <field name="room_type"/>
                        </group>
                        <group>
                            <field name="guest_id"/>
                            <field name="total_room_charges" widget="monetary"/>
                            <field name="company_id" groups="base.group_multi_company"/>
                        </group>
                    </group>
                    <notebook>
                        <page string="POS Orders">
                            <field name="pos_order_ids" readonly="1">
                                <list>
                                    <field name="name"/>
                                    <field name="date_order"/>
                                    <field name="partner_id"/>
                                    <field name="amount_total" widget="monetary"/>
                                    <field name="state"/>
                                </list>
                            </field>
                        </page>
                    </notebook>
                </sheet>
            </form>
        </field>
    </record>

    <!-- Hotel Room Action -->
    <record id="action_hotel_room" model="ir.actions.act_window">
        <field name="name">Hotel Rooms</field>
        <field name="res_model">hotel.room</field>
        <field name="view_mode">list,form</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create your first hotel room!
            </p>
            <p>
                Manage hotel rooms, assign guests, and track room charges.
            </p>
        </field>
    </record>

    <!-- Menu Items -->
    <menuitem id="menu_hotel_main" name="Hotel Management" parent="" sequence="100"/>
    <menuitem id="menu_hotel_rooms" name="Rooms" parent="menu_hotel_main" action="action_hotel_room" sequence="10"/>
</odoo>