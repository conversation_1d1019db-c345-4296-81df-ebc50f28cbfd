# Hotel POS Room Transfer

A comprehensive Odoo 18 addon that enables restaurant POS users to transfer orders to hotel rooms for later payment at checkout.

## Features

- **Transfer to Room**: Add a "Transfer to Room" button in the POS payment screen
- **Guest Management**: Manage hotel guests with check-in/check-out functionality
- **Room Management**: Track room status, occupancy, and charges
- **Room Charges**: Accumulate restaurant charges on guest rooms
- **Integration**: Seamless integration with POS Restaurant module
- **Search & Filter**: Easy search for rooms and guests in POS interface

## Installation Steps

### 1. Prerequisites
Make sure you have these modules installed:
- `point_of_sale`
- `pos_restaurant`
- `sale`
- `account`

### 2. Install the Addon

1. Copy the `hotel_pos_room_transfer` folder to your `custom_addons` directory

2. Update the addon list:
```bash
python3 ./odoo-bin -c temp_odoo.conf -d admin -u all --stop-after-init
```

3. Install the addon:
```bash
python3 ./odoo-bin -c temp_odoo.conf -d admin -i hotel_pos_room_transfer --http-port=8080
```

### 3. Configuration

1. **Go to Point of Sale > Configuration > Point of Sale**
2. **Edit your POS configuration**
3. **Navigate to the "Hotel Settings" tab**
4. **Enable "Enable Room Transfer"**
5. **Select "Room Transfer" as the payment method**
6. **Save the configuration**

### 4. Setup Demo Data (Optional)

The addon includes demo data with sample guests and rooms. This will be installed automatically if you install with demo data enabled.

## Usage Guide

### Setup Hotel Data

1. **Create Hotel Guests**:
   - Go to **Point of Sale > Hotel Management > Guests**
   - Click **Create** and fill in guest information
   - Set check-in date and other details

2. **Create Hotel Rooms**:
   - Go to **Point of Sale > Hotel Management > Rooms**
   - Click **Create** and add room details
   - Set room status to "Occupied" and assign a guest

### Using Room Transfer in POS

1. **Open POS Restaurant**
2. **Create an order** with products
3. **Go to Payment Screen**
4. **Click "Transfer to Room" button**
5. **Search and select** the guest's room
6. **Add optional note** if needed
7. **Click "Transfer"** to complete

### Key Components

#### Models
- **hotel.room**: Manages hotel rooms and their status
- **hotel.guest**: Manages guest information and stay details
- **pos.order**: Extended to track room transfers

#### Views
- Room management interface
- Guest management with check-in/check-out
- POS configuration for hotel settings

#### JavaScript Components
- Room transfer popup with search functionality
- Payment screen integration
- Order model extensions

## Features in Detail

### Room Transfer Process
1. Customer places order in restaurant POS
2. Staff clicks "Transfer to Room" instead of taking payment
3. System shows list of occupied rooms with guest details
4. Staff selects appropriate room and confirms transfer
5. Order is marked as "paid" via room transfer
6. Charges accumulate on the guest's room folio

### Guest Management
- Complete guest profile with contact information
- Check-in and check-out date tracking
- Link to Odoo partner records
- Total charges calculation

### Room Management  
- Room status tracking (Available, Occupied, Cleaning, Maintenance)
- Guest assignment and room charges
- Integration with POS orders

## Security

The addon includes proper access rights:
- **POS Users**: Can view and create room transfers
- **POS Managers**: Full access to all hotel management features

## Technical Notes

- **Compatible with Odoo 18**
- **Uses modern JavaScript (ES6+)**
- **Follows Odoo 18 development patterns**
- **Responsive design for mobile POS devices**

## Troubleshooting

### Common Issues

1. **"Transfer to Room" button not visible**:
   - Check if "Enable Room Transfer" is enabled in POS configuration
   - Verify the addon is properly installed

2. **No rooms appear in transfer popup**:
   - Ensure rooms are created and marked as "Occupied"
   - Check that guests are assigned to rooms

3. **Payment method errors**:
   - Verify "Room Transfer" payment method is configured in POS settings

## Support

For issues or customizations, please check:
- Odoo logs for error messages
- POS configuration settings
- Hotel room and guest data setup

## License

LGPL-3