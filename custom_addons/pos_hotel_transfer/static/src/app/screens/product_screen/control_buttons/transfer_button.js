/** @odoo-module */

import { ControlButtons } from "@point_of_sale/app/screens/product_screen/control_buttons/control_buttons";
import { Component } from "@odoo/owl";
import { usePos } from "@point_of_sale/app/store/pos_hook";

export class TransferButton extends Component {
    static template = "pos_hotel_transfer.TransferButton";

    setup() {
        this.pos = usePos();
        console.log('Transfer to Hotel button component setup!');
    }

    onClick() {
        console.log('Transfer to Hotel button clicked!');
        window.alert("Transfer to Hotel functionality will be implemented here!");
    }
}

// Register the TransferButton component
ControlButtons.components = { ...ControlButtons.components, TransferButton };
