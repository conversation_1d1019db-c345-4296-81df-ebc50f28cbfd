{
    'name': "Table Lock (Odoo 18)",
    'summary': "Lock POS tables to prevent concurrent orders.",
    'description': "Locks a table when an employee is taking an order in POS Restaurant.",
    'author': "Trigonal Technology Pvt Ltd.",
    'website': "https://trigonaltechnology.com",
    'category': 'Point of Sale',
    'version': '********.0',
    'depends': ['point_of_sale', 'pos_restaurant', 'hr'],
    'data': [
        'security/ir.model.access.csv',  
    ],
    'assets': {
        'point_of_sale._assets_pos': [
            'table_lock_odoo_18/static/src/js/floor_screen.js',
            'table_lock_odoo_18/static/src/js/models.js',
            'table_lock_odoo_18/static/src/js/orderProgressPopup.js',
            'table_lock_odoo_18/static/src/js/productScreen.js',
            'table_lock_odoo_18/static/src/css/pos.css',
            # 'table_lock_odoo_18/static/src/xml/OrderProgressPopup.xml',
        ],
    },
    'license': 'LGPL-3',
} 