from odoo import fields, models, api
import logging
from datetime import datetime, timedelta
from odoo.exceptions import ValidationError

_logger = logging.getLogger(__name__)

class TableInfo(models.Model):
    _name = "table.lock.info"
    _description = "Table Lock Information for POS Restaurant"

    session_id = fields.Many2one('pos.session', string='Session', required=True, index=True,
                                 domain="[('state', '=', 'opened')]", readonly=True)
    table_id = fields.Integer(string='table id', required=True)
    is_open = fields.Boolean(string='Is Open', required=True)
    active = fields.Boolean(string="Active", default=True)
    employee_id = fields.Many2one('hr.employee', required=True)
    lock_time = fields.Datetime(string='lock date', default=lambda x: fields.Datetime.now())

    def clear_locks_during_close(self, sessionId):
        _logger.info("[TableLock] Clearing all locks for session %s", sessionId)
        table_locks = self.env['table.lock.info'].sudo().search([('session_id', '=', sessionId)])
        for lock in table_locks:
            _logger.info("[TableLock] Removing lock for table %s by employee %s", lock.table_id, lock.employee_id.name)
            lock.unlink()

    def cleanup_stale_locks(self):
        """Clean up locks that are older than 5 minutes"""
        timeout = datetime.now() - timedelta(minutes=5)
        stale_locks = self.sudo().search([
            ('lock_time', '<', timeout),
            ('is_open', '=', True)
        ])
        if stale_locks:
            _logger.info("[TableLock] Found %d stale locks to clean up", len(stale_locks))
            for lock in stale_locks:
                _logger.info("[TableLock] Cleaning stale lock: Table %s, Employee %s, Lock time %s",
                           lock.table_id, lock.employee_id.name, lock.lock_time)
            stale_locks.unlink()

    @api.model
    def update_lock(self, val_list):
        _logger.info("[TableLock] update_lock called with val_list: %s", val_list)

        # Validate input
        if not all(k in val_list for k in ['table_id', 'session_id', 'employee_id']):
            _logger.error("[TableLock] Missing required fields in val_list: %s", val_list)
            return {'status': True, 'employee_name': 'System Error'}

        # Validate employee exists - try hr.employee first, then res.users
        employee_id = val_list['employee_id']
        employee = self.env['hr.employee'].browse(employee_id)

        if not employee.exists():
            # Try to find hr.employee from res.users
            user = self.env['res.users'].browse(employee_id)
            if user.exists() and user.employee_id:
                employee = user.employee_id
                val_list['employee_id'] = employee.id  # Update to use hr.employee ID
                _logger.info("[TableLock] Found hr.employee %s for user %s", employee.id, user.name)
            else:
                _logger.error("[TableLock] Employee with ID %s does not exist (tried both hr.employee and res.users)", employee_id)
                return {'status': True, 'employee_name': 'Invalid Employee'}

        # First cleanup any stale locks
        self.cleanup_stale_locks()

        table_id = val_list['table_id']
        session_id = val_list['session_id']
        employee_id = val_list['employee_id']

        # Log current state
        _logger.info("[TableLock] Checking locks for table %s in session %s", table_id, session_id)

        # Use a more specific domain to find existing locks
        table_lock = self.search([
            ('session_id', '=', session_id),
            ('table_id', '=', table_id),
            ('is_open', '=', True)
        ], limit=1)

        if table_lock:
            _logger.info("[TableLock] Found existing lock: Table %s, Employee %s, Lock time %s",
                        table_lock.table_id, table_lock.employee_id.name, table_lock.lock_time)

        lock_status = {'status': False, 'employee_name': table_lock.employee_id.name if table_lock else ''}

        # If no lock exists or lock is stale, create/update for current employee
        if not table_lock:
            _logger.info("[TableLock] No existing lock found, creating new lock for employee %s", employee_id)
            val_list['lock_time'] = datetime.now()
            new_lock = self.create(val_list)
            _logger.info("[TableLock] Created new lock: %s", new_lock.id)
            lock_status['status'] = False
        # If locked by another employee
        elif employee_id != table_lock.employee_id.id:
            current_time = datetime.now()
            time_diff = (current_time - table_lock.lock_time).seconds
            _logger.info("[TableLock] Table locked by different employee. Time difference: %d seconds", time_diff)

            # Check if lock is stale (older than 60 seconds)
            if time_diff >= 60:
                _logger.info("[TableLock] Lock is stale, transferring to employee %s", employee_id)
                # Timeout expired, transfer lock
                table_lock.write({
                    'employee_id': employee_id,
                    'lock_time': current_time,
                    'is_open': True
                })
                lock_status['status'] = False
            else:
                _logger.info("[TableLock] Lock is still valid, denying access to employee %s", employee_id)
                lock_status['status'] = True
        else:
            # Same user, update lock time
            _logger.info("[TableLock] Same employee, updating lock time")
            table_lock.write({
                'lock_time': datetime.now(),
                'is_open': True
            })

        _logger.info("[TableLock] Final lock status: %s", lock_status)
        return lock_status

    def update_employee(self, vals_list):
        _logger.info("inside update member:: %s", vals_list)

        # Handle case where vals_list comes as a list from RPC call
        if isinstance(vals_list, list) and len(vals_list) > 0:
            vals_list = vals_list[0]
        old_employee = vals_list['old_employee']
        table_lock = self.env['table.lock.info'].search([('employee_id', '=', old_employee), ('is_open', '=', True)])
        if table_lock:
            for table in table_lock:
                table.employee_id = vals_list['new_employee']
        return True

    @api.model
    def close_lock(self, vals):
        _logger.info("[TableLock] close_lock called with vals: %s", vals)

        if 'employee_id' not in vals:
            _logger.error("[TableLock] Employee ID not passed to close table")
            return {
                "value": False,
                "message": "employee id not passed"
            }

        employee_id = vals['employee_id']
        session_id = vals['session_id']

        # Try to get hr.employee ID if user ID was passed
        employee = self.env['hr.employee'].browse(employee_id)
        if not employee.exists():
            user = self.env['res.users'].browse(employee_id)
            if user.exists() and user.employee_id:
                employee_id = user.employee_id.id
                _logger.info("[TableLock] Using hr.employee %s for user %s in close_lock", employee_id, user.name)

        # More specific search for active locks
        table_locks = self.search([
            ('session_id', '=', session_id),
            ('employee_id', '=', employee_id),
            ('is_open', '=', True)
        ])

        if not table_locks:
            _logger.info("[TableLock] No active tables found for employee %s in session %s", employee_id, session_id)
            return {
                "value": False,
                "message": "No active tables for this employee"
            }

        _logger.info("[TableLock] Closing %d locks for employee %s", len(table_locks), employee_id)
        for lock in table_locks:
            _logger.info("[TableLock] Closing lock for table %s", lock.table_id)

        table_locks.write({'is_open': False})

        return {
            "value": True,
            "message": "Table locked for employee id successful"
        }

    @api.model
    def update_time(self, val_list):
        _logger.info("[TableLock] update_time called with val_list: %s", val_list)

        employee_id = val_list['employee_id']
        session_id = val_list['session_id']
        table_id = val_list['table_id']

        # Try to get hr.employee ID if user ID was passed
        employee = self.env['hr.employee'].browse(employee_id)
        if not employee.exists():
            user = self.env['res.users'].browse(employee_id)
            if user.exists() and user.employee_id:
                employee_id = user.employee_id.id
                _logger.info("[TableLock] Using hr.employee %s for user %s in update_time", employee_id, user.name)

        table_lock = self.search([
            ('session_id', '=', session_id),
            ('employee_id', '=', employee_id),
            ('table_id', '=', table_id),
            ('is_open', '=', True)
        ], limit=1)

        status = False
        if table_lock:
            _logger.info("[TableLock] Updating lock time for table %s", table_id)
            status = True
            table_lock.write({'lock_time': datetime.now()})
        else:
            _logger.info("[TableLock] No active lock found to update time for table %s", table_id)

        return status