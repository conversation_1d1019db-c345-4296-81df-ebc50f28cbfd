import { AbstractAwaitablePopup } from "@point_of_sale/app/popup/abstract_awaitable_popup";
import { _t } from "@web/core/l10n/translation";
import { registry } from "@web/core/registry";

export class OrderProgressPopup extends AbstractAwaitablePopup {
    static template = "OrderProgressPopup";
    static defaultProps = {
        cancelText: _t("Cancel"),
        title: _t("Confirm ?"),
        body: "",
    };
}

registry.category("pos_popups").add("OrderProgressPopup", OrderProgressPopup); 