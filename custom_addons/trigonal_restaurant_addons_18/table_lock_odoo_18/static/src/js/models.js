import { patch } from "@web/core/utils/patch";
import { PosStore } from "@point_of_sale/app/store/pos_store";

console.log('[TableLock] 🔥 POS STORE PATCH LOADING...');

patch(PosStore.prototype, {
    async set_cashier(employee) {
        console.log('[TableLock] 👤 ===== CASHIER CHANGE EVENT =====');
        console.log('[TableLock] 👤 CASHIER CHANGE DETAILS:', {
            new_employee_name: employee?.name,
            new_employee_id: employee?.id,
            new_employee_employee_id: employee?.employee_id,
            current_employee_name: this.cashier?.name,
            current_employee_id: this.cashier?.id,
            current_employee_employee_id: this.cashier?.employee_id,
            timestamp: new Date().toISOString()
        });

        // Check if this is actually a different employee to avoid unnecessary processing
        const current_employee_id = this.cashier?.employee_id || this.cashier?.id;
        const new_employee_id = employee?.employee_id || employee?.id;

        console.log('[TableLock] 👤 EMPLOYEE COMPARISON:', {
            current_employee_id: current_employee_id,
            new_employee_id: new_employee_id,
            is_same_employee: current_employee_id === new_employee_id
        });

        if (current_employee_id === new_employee_id) {
            console.log('[TableLock] 👤 SAME EMPLOYEE - Skipping lock update');
            console.log('[TableLock] 👤 ===== CASHIER CHANGE SKIPPED =====');
            return await super.set_cashier(...arguments);
        }

        if (employee?.id) { // Always enable for testing
            console.log('[TableLock] 🔄 DIFFERENT EMPLOYEE - Processing lock transfer...');
            const old_employee_id = current_employee_id;

            try {
                console.log('[TableLock] 🚀 CALLING super.set_cashier...');
                await super.set_cashier(...arguments);
                console.log('[TableLock] ✅ super.set_cashier completed');

                // Only update locks if we have an order and different employees
                if (this.get_order() && old_employee_id && new_employee_id && old_employee_id !== new_employee_id) {
                    const load = { old_employee: old_employee_id, new_employee: new_employee_id };
                    console.log('[TableLock] 🔄 TRANSFERRING LOCKS:', {
                        from_employee: old_employee_id,
                        to_employee: new_employee_id,
                        has_order: !!this.get_order()
                    });

                    console.log('[TableLock] 🌐 CALLING SERVER - update_employee method...');
                    await this.data.call('table.lock.info', 'update_employee', [load]);
                    console.log('[TableLock] ✅ EMPLOYEE LOCKS TRANSFERRED SUCCESSFULLY');
                } else {
                    console.log('[TableLock] ⏭️ NO LOCK TRANSFER NEEDED:', {
                        has_order: !!this.get_order(),
                        old_employee_id: old_employee_id,
                        new_employee_id: new_employee_id,
                        different_employees: old_employee_id !== new_employee_id
                    });
                }
            } catch (error) {
                console.error('[TableLock] ❌ ERROR during cashier change:', error);
                console.error('[TableLock] ❌ Calling super.set_cashier as fallback...');
                // Still call super method even if lock update fails
                await super.set_cashier(...arguments);
            }
        } else {
            console.log('[TableLock] 👤 NO EMPLOYEE ID - Single employee mode');
            await super.set_cashier(...arguments);
        }
        console.log('[TableLock] 👤 ===== CASHIER CHANGE COMPLETE =====');
    },

    async setTableFromUi(table, orderUuid = null) {
        console.log('[TableLock] 🎯 setTableFromUi called for table:', table.name);
        console.log('[TableLock] 🎯 This is an alternative table selection path');

        if (true) { // Always enable locking for testing
            const table_id = table.id;
            const session_id = this.session?.id || this.pos_session?.id;
            const employee = this.cashier;

            // Get the actual hr.employee ID from the user
            const employee_id = employee?.employee_id || employee?.id;

            console.log('[TableLock] Checking table lock before setting table:', {
                table_id,
                session_id,
                employee: employee?.name,
                employee_id: employee_id
            });

            try {
                const response = await this.data.call('table.lock.info', 'update_lock', [], {
                    'val_list': {
                        'session_id': session_id,
                        'table_id': table_id,
                        'is_open': true,
                        'employee_id': employee_id
                    }
                });

                console.log('[TableLock] Lock check response in setTableFromUi:', response);

                if (response['status']) {
                    console.log('[TableLock] Table is locked by:', response['employee_name']);
                    // Use notification service instead of popup for better compatibility
                    this.notification.add(`${response['employee_name']} is taking order. Please wait and try again after sometime.`, {
                        type: 'warning',
                        sticky: false
                    });
                    return; // Don't proceed with table setting
                }
            } catch (error) {
                console.error('[TableLock] Error checking table lock in setTableFromUi:', error);
            }
        }

        // Proceed with normal table setting
        return await super.setTableFromUi(table, orderUuid);
    },

    async addLineToCurrentOrder(vals, opts = {}, configure = true) {
        console.log('[TableLock] 🛒 Adding product to order!', {
            productId: vals.product_id,
            quantity: vals.qty || 1,
            currentTable: this.get_order()?.table_id?.name,
            currentUser: this.cashier?.name,
            currentOrderLines: this.get_order()?.lines?.length || 0
        });

        const result = await super.addLineToCurrentOrder(vals, opts, configure);

        console.log('[TableLock] ✅ Product line added!', {
            newOrderLines: this.get_order()?.lines?.length || 0,
            orderTotal: this.get_order()?.get_total_with_tax() || 0
        });

        return result;
    },

    async showScreen(screenName, props) {
        console.log('[TableLock] 📺 ===== SCREEN CHANGE EVENT =====');
        console.log('[TableLock] 📺 SCREEN CHANGE DETAILS:', {
            new_screen: screenName,
            current_screen: this.mainScreen?.component?.name,
            props: props,
            current_user: this.cashier?.name,
            current_table: this.get_order()?.table_id?.name,
            session_id: this.session?.id || this.pos_session?.id,
            timestamp: new Date().toISOString()
        });

        // If navigating to FloorScreen from a table screen, release current user's locks
        if (screenName === 'FloorScreen' && this.mainScreen?.component?.name !== 'FloorScreen') {
            console.log('[TableLock] 🏠 NAVIGATION TO FLOORSCREEN DETECTED!');
            console.log('[TableLock] 🔓 Need to release locks before navigation...');

            const session_id = this.session?.id || this.pos_session?.id;
            const employee = this.cashier;

            if (session_id && employee) {
                const employee_id = employee?.employee_id || employee?.id;

                console.log('[TableLock] 🔓 LOCK RELEASE DATA:', {
                    session_id: session_id,
                    employee_name: employee.name,
                    employee_id: employee_id,
                    reason: 'Navigation to FloorScreen'
                });

                try {
                    console.log('[TableLock] 🌐 CALLING SERVER - close_lock method...');
                    const response = await this.data.call('table.lock.info', 'close_lock', [], {
                        'vals': {
                            'session_id': session_id,
                            'employee_id': employee_id
                        }
                    });
                    console.log('[TableLock] ✅ LOCKS RELEASED SUCCESSFULLY:', response);
                    console.log('[TableLock] ✅ User can now navigate to FloorScreen');
                } catch (error) {
                    console.error('[TableLock] ❌ ERROR releasing locks on screen change:', error);
                }
            } else {
                console.log('[TableLock] ⚠️ Missing session or employee data for lock release');
            }
        } else {
            console.log('[TableLock] 📺 NO LOCK RELEASE NEEDED:', {
                reason: screenName === 'FloorScreen' ? 'Already on FloorScreen' : 'Not navigating to FloorScreen',
                target_screen: screenName,
                current_screen: this.mainScreen?.component?.name
            });
        }

        // Call the original showScreen method with error handling
        try {
            console.log('[TableLock] 🚀 CALLING ORIGINAL showScreen method...');
            const result = super.showScreen(screenName, props);
            console.log('[TableLock] ✅ showScreen completed successfully');
            console.log('[TableLock] 📺 ===== SCREEN CHANGE COMPLETE =====');
            return result;
        } catch (error) {
            console.error('[TableLock] ❌ ERROR in showScreen:', error);
            console.error('[TableLock] ❌ Attempting fallback...');
            // Try to call without our modifications if there's an error
            return super.showScreen(screenName, props);
        }
    }
});