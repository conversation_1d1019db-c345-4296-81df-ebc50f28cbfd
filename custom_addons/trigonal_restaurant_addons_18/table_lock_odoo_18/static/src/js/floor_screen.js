import { patch } from "@web/core/utils/patch";
import { FloorScreen } from "@pos_restaurant/app/floor_screen/floor_screen";
import { useService } from "@web/core/utils/hooks";

console.log('[TableLock] 🔥 FLOOR SCREEN PATCH LOADING...');

patch(FloorScreen.prototype, {
    setup() {
        console.log('[TableLock] 🚀 FloorScreen patch applied - setup() called');
        super.setup();
        this.notification = useService("notification");
        this.orm = useService("orm");
        this.lockCheckInterval = null;
        this.lastLockCheck = null;
        console.log('[TableLock] ✅ FloorScreen setup complete');
    },

    async mounted() {
        console.log('[TableLock] 🎬 FloorScreen MOUNTED - Starting initialization...');
        const session_id = this.pos.session?.id || this.pos.pos_session?.id;
        const employee = this.pos.cashier;

        console.log('[TableLock] 📊 MOUNT DATA:', {
            session_id: session_id,
            employee_name: employee?.name,
            employee_id: employee?.id,
            employee_employee_id: employee?.employee_id,
            total_employees: this.pos.employees?.length || 0,
            current_floor: this.pos.currentFloor?.name,
            tables_count: this.pos.currentFloor?.table_ids?.length || 0
        });

        if (true) { // Always enable locking for testing
            console.log('[TableLock] 🔒 LOCK SYSTEM ENABLED - Initializing...');

            // Get the actual hr.employee ID from the user
            const employee_id = employee?.employee_id || employee?.id;
            console.log('[TableLock] 👤 Using employee_id for locks:', employee_id);

            try {
                console.log('[TableLock] 🧹 CLEANING existing locks for employee on mount...');
                const closeResponse = await this.orm.call('table.lock.info', 'close_lock', [], {
                    'vals': {
                        'session_id': session_id,
                        'employee_id': employee_id
                    }
                });
                console.log('[TableLock] ✅ CLEAN LOCKS RESPONSE:', closeResponse);
            } catch (error) {
                console.error('[TableLock] ❌ ERROR cleaning existing locks:', error);
            }

            this.lockCheckInterval = setInterval(() => {
                this._checkAndUpdateLocks();
            }, 30000);

            console.log('[TableLock] ⏰ Lock check interval initialized (30s)');
        } else {
            console.log('[TableLock] 👤 Single employee mode, lock system disabled');
        }

        console.log('[TableLock] 🎬 Calling super.mounted()...');
        await super.mounted();
        console.log('[TableLock] ✅ FloorScreen MOUNTED COMPLETE!');
    },

    async willUnmount() {
        console.log('[TableLock] Component unmounting');

        if (this.lockCheckInterval) {
            console.log('[TableLock] Clearing lock check interval');
            clearInterval(this.lockCheckInterval);
        }

        const session_id = this.pos.session?.id || this.pos.pos_session?.id;
        const employee = this.pos.cashier;

        if (true) { // Always enable locking for testing
            // Get the actual hr.employee ID from the user
            const employee_id = employee?.employee_id || employee?.id;

            try {
                console.log('[TableLock] Closing locks before unmount');
                const response = await this.orm.call('table.lock.info', 'close_lock', [], {
                    'vals': {
                        'session_id': session_id,
                        'employee_id': employee_id
                    }
                });
                console.log('[TableLock] Close locks response:', response);
            } catch (error) {
                console.error('[TableLock] Error closing locks during unmount:', error);
            }
        }

        await super.willUnmount();
    },

    async _checkAndUpdateLocks() {
        const session_id = this.pos.pos_session.id;
        const employee = this.pos.cashier;
        const currentTime = new Date().getTime();

        if (this.lastLockCheck && (currentTime - this.lastLockCheck) < 5000) {
            console.log('[TableLock] Skipping lock check - too soon since last check');
            return;
        }

        this.lastLockCheck = currentTime;

        if (!this.selectedTable) {
            console.log('[TableLock] No selected table, skipping lock update');
            return;
        }

        console.log('[TableLock] Updating lock for table:', this.selectedTable.id);

        // Get the actual hr.employee ID from the user
        const employee_id = employee?.employee_id || employee?.id;

        try {
            const response = await this.orm.call('table.lock.info', 'update_time', [], {
                'val_list': {
                    'session_id': session_id,
                    'employee_id': employee_id,
                    'table_id': this.selectedTable.id
                }
            });
            console.log('[TableLock] Lock update response:', response);
        } catch (error) {
            console.error('[TableLock] Error updating lock time:', error);
        }
    },

    async onClickTable(table) {
        console.log('[TableLock] 🎯 ===== TABLE CLICK EVENT STARTED =====');
        console.log('[TableLock] 🎯 TABLE DETAILS:', {
            table_name: table.name,
            table_id: table.id,
            table_number: table.table_number,
            floor_name: table.floor_id?.name,
            current_user: this.pos.cashier?.name,
            current_user_id: this.pos.cashier?.id,
            session_id: this.pos.session?.id || this.pos.pos_session?.id
        });

        if (true) { // Always enable locking for testing
            console.log('[TableLock] 🔒 LOCK CHECK MODE - Checking if table is available...');
            const table_id = table.id;
            const session_id = this.pos.session?.id || this.pos.pos_session?.id;
            const employee = this.pos.cashier;

            // Get the actual hr.employee ID from the user
            const employee_id = employee?.employee_id || employee?.id;

            console.log('[TableLock] 📊 LOCK REQUEST DATA:', {
                table_id: table_id,
                table_name: table.name,
                session_id: session_id,
                employee_name: employee?.name,
                employee_id: employee_id,
                employee_employee_id: employee?.employee_id,
                timestamp: new Date().toISOString()
            });

            try {
                console.log('[TableLock] 🌐 CALLING SERVER - update_lock method...');
                const response = await this.orm.call('table.lock.info', 'update_lock', [], {
                    'val_list': {
                        'session_id': session_id,
                        'table_id': table_id,
                        'is_open': true,
                        'employee_id': employee_id
                    }
                });

                console.log('[TableLock] 📥 SERVER RESPONSE RECEIVED:', {
                    response: response,
                    status: response['status'],
                    employee_name: response['employee_name'],
                    is_locked: response['status'] === true,
                    can_access: response['status'] === false
                });

                if (!response['status']) {
                    console.log('[TableLock] ✅ TABLE IS FREE! User can access table');
                    console.log('[TableLock] 🚀 PROCEEDING to select table...');
                    await super.onClickTable(table);
                    console.log('[TableLock] ✅ TABLE SELECTION COMPLETED SUCCESSFULLY!');
                } else {
                    console.log('[TableLock] ❌ TABLE IS LOCKED!');
                    console.log('[TableLock] 🔒 Locked by employee:', response['employee_name']);
                    console.log('[TableLock] 🚫 BLOCKING access and showing notification...');
                    this.notification.add(
                        `${response['employee_name']} is taking order. Please wait and try again after sometime.`,
                        {
                            type: 'warning',
                            sticky: false,
                            autocloseDelay: 1500  // Auto-dismiss after 1.5 seconds
                        }
                    );
                    console.log('[TableLock] 🚫 NOTIFICATION SHOWN - Access denied (auto-dismiss in 1.5s)');
                }
            } catch (error) {
                console.error('[TableLock] ❌ ERROR during table lock check:', error);
                console.error('[TableLock] ❌ Error details:', error.data);
                console.error('[TableLock] ❌ Error message:', error.message);
                this.notification.add(
                    `Table lock error: ${error.message || 'Unable to check table lock status'}`,
                    {
                        type: 'danger',
                        sticky: false,
                        autocloseDelay: 1500  // Auto-dismiss after 1.5 seconds for errors
                    }
                );
                console.log('[TableLock] ⚠️ ERROR occurred - proceeding with table selection anyway...');
                await super.onClickTable(table);
            }
        } else {
            console.log('[TableLock] 👤 Single employee mode - no lock check needed');
            console.log('[TableLock] ✅ Proceeding directly to table selection...');
            await super.onClickTable(table);
            console.log('[TableLock] ✅ Table selection completed (single user)!');
        }
        console.log('[TableLock] 🎯 ===== TABLE CLICK EVENT FINISHED =====');
    }
});