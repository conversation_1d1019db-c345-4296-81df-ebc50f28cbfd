import { patch } from "@web/core/utils/patch";
import { ProductScreen } from "@point_of_sale/app/screens/product_screen/product_screen";
import { useService } from "@web/core/utils/hooks";

console.log('[TableLock] 🔥 PRODUCT SCREEN PATCH LOADING...');

patch(ProductScreen.prototype, {
    setup() {
        super.setup();
        this.orm = useService("orm");
    },

    async _onClickProduct(event) {
        const product = event.detail;
        console.log('[TableLock] 🍕 PRODUCT CLICKED!', {
            productName: product.display_name,
            productId: product.id,
            currentTable: this.pos.get_order()?.table?.name,
            currentUser: this.pos.cashier?.name,
            orderLines: this.pos.get_order()?.lines?.length || 0
        });
        
        // Call parent method
        await super._onClickProduct(event);
        
        console.log('[TableLock] ✅ Product added to order!', {
            totalLines: this.pos.get_order()?.lines?.length || 0,
            orderTotal: this.pos.get_order()?.get_total_with_tax() || 0
        });
    },

    async update_time() {
        console.log('[TableLock] 🕐 Updating lock time for current table...');
        console.log('[TableLock] 🕐 Current user:', this.pos.cashier?.name);
        console.log('[TableLock] 🕐 Current table:', this.pos.get_order()?.table?.name);
        
        try {
            await this.orm.call('table.lock.info', 'update_time', [], {
                'val_list': {
                    'session_id': this.pos.session?.id || this.pos.pos_session?.id,
                    'employee_id': this.pos.cashier.id,
                    'table_id': this.pos.get_order().table?.id,
                }
            });
            console.log('[TableLock] ✅ Lock time updated successfully');
        } catch (error) {
            console.error('[TableLock] ❌ Error updating lock time:', error);
        }
    },

    async mounted() {
        console.log('[TableLock] 🎬 ProductScreen mounted!');
        console.log('[TableLock] 🎬 Current order:', this.pos.get_order()?.name || 'No order');
        console.log('[TableLock] 🎬 Current table:', this.pos.get_order()?.table?.name || 'No table');
        
        await super.mounted();
        
        if (this.pos.get_order()?.table) { // Always enable if there's a table
            console.log('[TableLock] 🚀 Starting lock refresh timer (every 5 seconds)...');
            this.lockInterval = setInterval(this.update_time.bind(this), 5000);
        } else {
            console.log('[TableLock] ⏸️ Not starting lock timer (single user or no table)');
        }
    },

    willUnmount() {
        console.log('[TableLock] 🛑 ProductScreen unmounting...');
        if (this.lockInterval) {
            console.log('[TableLock] 🛑 Stopping lock refresh timer');
            clearInterval(this.lockInterval);
        }
        super.willUnmount();
    }
}); 