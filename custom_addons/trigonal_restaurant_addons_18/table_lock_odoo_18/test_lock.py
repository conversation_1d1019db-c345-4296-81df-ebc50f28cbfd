#!/usr/bin/env python3
"""
Simple test script to verify table lock functionality
"""
import requests
import json

# Configuration
ODOO_URL = 'http://localhost:8080'
DB_NAME = 'admin'
USERNAME = 'admin'
PASSWORD = 'admin'

def odoo_rpc(method, model, args=None, kwargs=None):
    """Make RPC call to Odoo"""
    url = f"{ODOO_URL}/jsonrpc"
    headers = {'Content-Type': 'application/json'}
    
    # First authenticate
    auth_data = {
        'jsonrpc': '2.0',
        'method': 'call',
        'params': {
            'service': 'common',
            'method': 'authenticate',
            'args': [DB_NAME, USERNAME, PASSWORD, {}]
        },
        'id': 1
    }
    
    response = requests.post(url, data=json.dumps(auth_data), headers=headers)
    if response.status_code != 200:
        raise Exception(f"Authentication failed: {response.text}")
    
    auth_result = response.json()
    if 'error' in auth_result:
        raise Exception(f"Authentication error: {auth_result['error']}")
    
    uid = auth_result['result']
    if not uid:
        raise Exception("Authentication failed - no user ID returned")
    
    # Now make the actual call
    call_data = {
        'jsonrpc': '2.0',
        'method': 'call',
        'params': {
            'service': 'object',
            'method': 'execute_kw',
            'args': [DB_NAME, uid, PASSWORD, model, method, args or [], kwargs or {}]
        },
        'id': 2
    }
    
    response = requests.post(url, data=json.dumps(call_data), headers=headers)
    if response.status_code != 200:
        raise Exception(f"RPC call failed: {response.text}")
    
    result = response.json()
    if 'error' in result:
        raise Exception(f"RPC error: {result['error']}")
    
    return result['result']

def test_table_lock():
    """Test table lock functionality"""
    print("Testing table lock functionality...")
    
    try:
        # Test 1: Get POS session
        sessions = odoo_rpc('search_read', 'pos.session', [[('state', '=', 'opened')]], {'limit': 1})
        if not sessions:
            print("No open POS session found. Please open a POS session first.")
            return
        
        session_id = sessions[0]['id']
        print(f"Found POS session: {session_id}")
        
        # Test 2: Get employees
        employees = odoo_rpc('search_read', 'hr.employee', [], {'limit': 2})
        if len(employees) < 2:
            print("Need at least 2 employees to test table lock")
            return
        
        employee1_id = employees[0]['id']
        employee2_id = employees[1]['id']
        print(f"Testing with employees: {employee1_id} ({employees[0]['name']}) and {employee2_id} ({employees[1]['name']})")
        
        # Test 3: Try to lock table with first employee
        table_id = 'table_1'
        lock_data = {
            'session_id': session_id,
            'table_id': table_id,
            'is_open': True,
            'employee_id': employee1_id
        }
        
        print(f"Attempting to lock table {table_id} with employee {employee1_id}")
        result1 = odoo_rpc('update_lock', 'table.lock.info', [[lock_data]])
        print(f"First lock result: {result1}")
        
        # Test 4: Try to lock same table with second employee
        lock_data['employee_id'] = employee2_id
        print(f"Attempting to lock table {table_id} with employee {employee2_id}")
        result2 = odoo_rpc('update_lock', 'table.lock.info', [[lock_data]])
        print(f"Second lock result: {result2}")
        
        if result2.get('status') == True:
            print("✓ Table lock is working! Second employee was blocked.")
        else:
            print("✗ Table lock is NOT working! Second employee was allowed.")
        
        # Test 5: Close locks
        close_data = {
            'session_id': session_id,
            'employee_id': employee1_id
        }
        print(f"Closing locks for employee {employee1_id}")
        result3 = odoo_rpc('close_lock', 'table.lock.info', [[close_data]])
        print(f"Close lock result: {result3}")
        
    except Exception as e:
        print(f"Error during testing: {e}")

if __name__ == '__main__':
    test_table_lock()