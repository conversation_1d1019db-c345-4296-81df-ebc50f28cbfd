<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="ird_report_pdf_paper_format" model="report.paperformat">
        <field name="name">IRD report format</field>
        <field name="default" eval="True"/>
        <field name="format">A4</field>
        <field name="page_height">0</field>
        <field name="page_width">0</field>
        <field name="orientation">Landscape</field>
        <field name="margin_top">5</field>
        <field name="margin_bottom">5</field>
        <field name="margin_left">5</field>
        <field name="margin_right">5</field>
        <field name="header_line" eval="False"/>
        <field name="header_spacing">0</field>
    </record>

    <report
        id="ird_report_pdf"
        string="Ird Report"
        model="trigonal.ird.report"
        report_type="qweb-pdf"
        name="trigonal_ird.ird_report_template"
        file="trigonal_ird.ird_report_template"
        paperformat="trigonal_ird.ird_report_pdf_paper_format"
        />

    <report
            id="account.account_invoices"
            model="account.move"
            string="Invoices"
            report_type="qweb-pdf"
            name="trigonal_ird.report_invoice_with_payments"
            file="trigonal_ird.report_invoice_with_payments"
            attachment="(object.state == 'posted') and ((object.name or 'INV').replace('/','_')+'.pdf')"
            print_report_name="(object._get_report_base_filename())"
            groups="account.group_account_invoice"
            menu="False"
            paperformat="trigonal_account.trigonal_pdf_paper_format"
        />

    <report
            id="account.account_invoices_without_payment"
            model="account.move"
            string="Invoices without Payment"
            report_type="qweb-pdf"
            name="trigonal_ird.report_invoice"
            file="trigonal_ird.report_invoice"
            attachment="(object.state == 'posted') and ((object.name or 'INV').replace('/','_')+'.pdf')"
            print_report_name="(object._get_report_base_filename())"
            menu="False"
            paperformat="trigonal_account.trigonal_pdf_paper_format"
        />

</odoo>