<?xml version="1.0" encoding="UTF-8"?>
    <odoo>
        <data>
            <record model="ir.ui.view" id="trigonal_ird.ird_report_view">
                <field name="name">Trigonal IRD Report</field>
                <field name="model">trigonal.ird.report</field>
                <field name="arch" type="xml">
                    <tree create="false" edit="false" delete="false" export_xlsx="true">
                        <field name="fiscal_year" optional="show"/>
                        <field name="bill_number" optional="show"/>
                        <field name="sync_with_ird" optional="show"/>
                        <field name="customer_name" optional="show"/>
                        <field name="customer_pan" optional="hide"/>
                        <field name="bill_date" optional="hide"/>
                        <field name="amount" optional="hide"/>
                        <field name="discount" optional="hide"/>
                        <field name="taxable_amount" optional="hide"/>
                        <field name="tax_amount" optional="hide"/>
                        <field name="total_amount" optional="show"/>
                        <field name="is_bill_printed" optional="hide"/>
                        <field name="is_bill_active" optional="hide"/>
                        <field name="printed_time" optional="hide"/>
                        <field name="entered_by" optional="hide"/>
                        <field name="printed_by" optional="hide"/>
                        <field name="is_realtime" optional="hide"/>
                        <field name="payment_method" optional="hide"/>
                        <field name="vat_refund_amount" optional="hide"/>
                        <field name="transaction_id" optional="hide"/>
                        <field name="last_sync_status" optional="show"/>
                    </tree>
                </field>
            </record>
            <record id="trigonal_ird.ird_report_action" model="ir.actions.act_window">
                <field name="name">IRD Report</field>
                <field name="res_model">Trigonal.ird.report</field>
                <field name="view_mode">tree</field>
                <field name="context">{'active_test': False}</field>
            </record>
            <record id="ird_data_sync_to_cbms" model="ir.actions.server">
                <field name="name">Sync to CBMS</field>
                <field name="model_id" ref="trigonal_ird.model_trigonal_ird_report"/>
                <field name="binding_model_id" ref="trigonal_ird.model_trigonal_ird_report"/>
                <field name="binding_view_types">list</field>
                <field name="state">code</field>
                <field name="code">action = records.batch_ird_report_sync()</field>
            </record>

            <record id="trigonal_ird.ird_report_search" model="ir.ui.view">
                <field name="name">ird_report_search</field>
                <field name="model">trigonal.ird.report</field>
                <field name="arch" type="xml">
                    <search>
                        <filter string="Fiscal Year" name="group_by_fiscal_year" context="{'group_by' : 'fiscal_year'}"/>
                    </search>
                </field>
            </record>

            <menuitem action="trigonal_ird.ird_report_action"
                id="ird_report_menu" name="IRD report"
                parent="account.menu_finance_reports" sequence="10" groups="account.group_account_user"/>
        </data>
    </odoo>