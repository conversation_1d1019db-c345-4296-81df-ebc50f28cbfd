<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record id="tds_payable_report_wizard" model="ir.ui.view">
        <field name="name">tds.payable.reporting.wizard</field>
        <field name="model">tds.payable.report</field>
        <field name="arch" type="xml">
            <form string="TDS Payable Report">
                <field name="state" invisible="1"/>
                <div invisible="state != 'get'">
                    <group>
                        <field name="invoice_data" colspan="4" invisible="1"/>
                        <field name="file_name" filename="invoice_data" colspan="4"/>
                    </group>
                </div>
                <div invisible="state != 'get'">
                    <p class="oe_grey">
                        Do You want to Print TDS Payable Report ?
                    </p>
                    <group>
                        <group>
                            <field name="date_from"/>
                        </group>
                        <group>
                            <field name="date_to"/>
                        </group>
                    </group>
                </div>
                <footer invisible="state != 'get'">
                    <button string="Generate Excel Report"
                            class="btn-primary"
                            name="action_tds_payable_report"
                            type="object"/>

                    <button string="Cancel"
                            class="btn-default"
                            special="cancel"/>
                </footer>
                <footer invisible="state != 'get'">
                    <button special="cancel" string="Ok" class="oe_highlight"/>
                    <button special="cancel" string="Cancel"/>
                </footer>
            </form>
        </field>
    </record>

    <record id="action_tds_payable_report" model="ir.actions.act_window">
        <field name="name">TDS Payable Report</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">tds.payable.report</field>
        <field name="view_mode">form</field>
        <field name="view_id" ref="tds_payable_report_wizard"/>
        <field name="target">new</field>
    </record>

    <menuitem name="TDS Payable Report"
              id="nexus_tds_payable_report"
              parent="account.menu_finance_reports"
              sequence="200"
              action="action_tds_payable_report"/>

</odoo>
