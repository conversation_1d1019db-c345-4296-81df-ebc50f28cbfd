<?xml version="1.0" encoding="UTF-8"?>
<odoo>
    <record id="res_config_settings_ird_config_form" model="ir.ui.view">
        <field name="name">res.config.settings.ird.config.form</field>
        <field name="model">res.config.settings</field>
        <!-- <field eval="250" name="priority"/> -->
        <field name="inherit_id" ref="account.res_config_settings_view_form"/>
         <field name="arch" type="xml">
            <xpath expr="//block[@id='pay_invoice_online_setting_container']" position="after">
                <div groups="account.group_account_manager">
                    <h2>IRD Configuration</h2>
                    <div class="row mt16 o_settings_container">
                        <div class="col-12 col-lg-6 o_setting_box">
                            <div class="o_setting_left_pane"/>
                            <div class="o_setting_right_pane">
                                <div class="content-group">
                                    <div class="row mt16">
                                        <label for="ird_username" class="col-lg-3 o_light_label"/>
                                        <field name="ird_username"/>
                                    </div>
                                    <div class="row mt16">
                                        <label for="ird_password" class="col-lg-3 o_light_label"/>
                                        <field name="ird_password"/>
                                    </div>
                                    <div class="row mt16">
                                        <label for="ird_url" class="col-lg-3 o_light_label"/>
                                        <field name="ird_url"/>
                                    </div>
                                    <div class="row mt16">
                                        <label for="ird_realtime_data_sync" class="col-lg-3 o_light_label"/>
                                        <field name="ird_realtime_data_sync"/>
                                    </div>
                                    <div class="row mt16" attrs="{'invisible': ['|',('ird_realtime_data_sync', '=', True), ('ird_realtime_data_sync', '=', 'True')]}">
                                        <label for="ird_time_to_sync_data" class="col-lg-3 o_light_label"/>
                                        <field name="ird_time_to_sync_data" widget="float_time" attrs="{'required': [('ird_realtime_data_sync', '=', False)]}"/>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </xpath>
        </field>
    </record>
</odoo>