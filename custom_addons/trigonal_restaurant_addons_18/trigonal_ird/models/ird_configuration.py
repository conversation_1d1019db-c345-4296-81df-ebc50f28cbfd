import logging

from odoo import api, fields, models
from datetime import datetime, timedelta
import math
_logger = logging.getLogger(__name__)

class IRDConfiguration(models.TransientModel):
    _inherit = 'res.config.settings'

    ird_username = fields.Char(string="Username")
    ird_password = fields.Char(string="Password")
    ird_url = fields.Char(string="URl")
    ird_realtime_data_sync = fields.Boolean(string="Realtime Sync Data")
    ird_time_to_sync_data = fields.Float(string="Time to sync data")

    def set_values(self):
        super(IRDConfiguration, self).set_values()
        try:
            now = datetime.now()
            minutes, hour = math.modf(self.ird_time_to_sync_data)
            minutes = int(minutes*60)
            hour = int(hour)
            new_date_to_run_schedule = now.replace(hour=hour, minute=minutes)
            new_date_to_run_schedule = new_date_to_run_schedule if new_date_to_run_schedule > now else new_date_to_run_schedule + timedelta(days=1)
            cron = self.sudo().env.ref('trigonal_ird.ird_corn_bill_sync')
            cron.sudo().write({'nextcall': new_date_to_run_schedule})
        except Exception as e:
            _logger.info("Exception occured")
            _logger.info(e)

        self.env['ir.config_parameter'].sudo().set_param('ird.ird_username', self.ird_username)
        self.env['ir.config_parameter'].sudo().set_param('ird.ird_password', self.ird_password)
        self.env['ir.config_parameter'].sudo().set_param('ird.ird_url', self.ird_url)
        self.env['ir.config_parameter'].sudo().set_param('ird.ird_realtime_data_sync', self.ird_realtime_data_sync)
        self.env['ir.config_parameter'].sudo().set_param('ird.ird_time_to_sync_data', self.ird_time_to_sync_data)

    @api.model
    def get_values(self):
        res = super(IRDConfiguration, self).get_values()
        res.update(
            ird_username=self.env['ir.config_parameter'].sudo().get_param('ird.ird_username'),
            ird_password=self.env['ir.config_parameter'].sudo().get_param('ird.ird_password'),
            ird_url=self.env['ir.config_parameter'].sudo().get_param('ird.ird_url'),
            ird_realtime_data_sync=self.env['ir.config_parameter'].sudo().get_param('ird.ird_realtime_data_sync'),
            ird_time_to_sync_data=self.env['ir.config_parameter'].sudo().get_param('ird.ird_time_to_sync_data'),
        )
        return res