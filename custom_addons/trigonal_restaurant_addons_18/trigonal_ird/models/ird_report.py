import logging

from odoo import api, fields, models, _
import requests
from datetime import datetime
from odoo.addons.trigonal_ird.services.cbms_service import CBMSService
from nepali_date import NepaliDate

_logger = logging.getLogger(__name__)


class IRDReport(models.Model):
    _name = "ird.report"
    _description = "Table to track invoices across the system"

    fiscal_year = fields.Char(string='Fiscal Year')
    bill_number = fields.Char(string='Invoice Number')
    customer_name = fields.Char(string="Customer Name", help="Name of the customer for whom customer name is created")
    customer_pan = fields.Char(string="Customer PAN number", help="PAN number of the customer")
    bill_date = fields.Datetime(string="Billed date", help="Date time in which bill is crated")
    amount = fields.Float(string="Sub Total")
    discount = fields.Float(string="Discount")
    taxable_amount = fields.Float(string="Taxable Amount")
    tax_amount = fields.Float(string="Tax Amount")
    total_amount = fields.Float(string="Total Amount")
    sync_with_ird = fields.Boolean(string="Synced With IRD")
    is_bill_printed = fields.Boolean(string="Invoice Printed Status")
    is_bill_active = fields.Boolean(string="Invoice Status")
    printed_time = fields.Datetime(string="Invoice Printed Time")
    entered_by = fields.Char(string="Created By")
    printed_by = fields.Char(string="Printed By")
    is_realtime = fields.Boolean(string="Real time")
    payment_method = fields.Char(string="Payment Method")
    vat_refund_amount = fields.Float(string="VAT Refund Amount")
    transaction_id = fields.Char(string="Transaction Id")
    type = fields.Char(string="Invoice type")
    ref_invoice_number = fields.Char(string="Reference invoice number", help="This is used for credit note scenario")
    reason_for_return = fields.Char(string="Return reason", help="Reason why this credit note is created.")
    last_sync_status = fields.Char(string="Latest sync status", help="Reason why the sync ")

    def print_pdf_ird_report(self):
        data = {
            'model_id': self.id,
        }
        return self.env.ref('trigonal_ird.print_ird_report_pdf').report_action(self, data=data)

    def sync_invoice_in_ird(self):
        service = CBMSService()
        config = self.env['res.config.settings']
        config_vals = config.get_values()
        data = self.prepare_data_to_sync(config_vals['ird_username'], config_vals['ird_password'])
        response = service.sync_bill_data_to_cbms(data, self.type, config_vals['ird_url'])
        self.last_sync_status = response['last_sync_status']
        params = {
            'sticky': False
        }

        if response['synced']:
            self.sync_with_ird = True
            params.update({
                'title': _('CBMS Sync Success'),
                'message': 'Invoice: ' + self.bill_number + ' successfully synced to CBMS system',
                'type': 'success',
            })
        else:
            self.is_realtime = False
            self.sync_with_ird = False
            params.update({
                'title': _('CBMS Sync Failure'),
                'message': 'Invoice: ' + self.bill_number + ' failed to sync in CBMS system',
                'type': 'danger',
            })
        notification = {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': params,
            'sync_with_ird': self.sync_with_ird
        }
        _logger.info(notification)
        return notification

    def prepare_data_to_sync(self, username, password):
        active_company_vat = self.env.user.company_id.vat
        result = {
            "username": username,
            "password": password,
            "seller_pan": active_company_vat,
            "buyer_pan": self.customer_pan,
            "buyer_name": self.customer_name,
            "fiscal_year": self.fiscal_year,
            "total_sales": self.total_amount,
            "taxable_sales_vat": self.taxable_amount,
            "vat": self.tax_amount,
            "excisable_amount": 0,
            "excise": 0,
            "taxable_sales_hst": 0,
            "hst": 0,
            "amount_for_esf": 0,
            "esf": 0,
            "export_sales": 0,
            "tax_exempted_sales": 0,
            "isrealtime": self.is_realtime,
            "datetimeclient": datetime.now().strftime("%Y.%m.%d %H:%M:%S")
        }
        billed_date = NepaliDate.to_nepali_date(self.bill_date.date()).strfdate('%Y.%m.%d')
        if self.type == 'out_invoice':
            result.update({
                "invoice_number": self.bill_number,
                "invoice_date": billed_date
            })
        elif self.type == 'out_refund':
            result.update({
                "ref_invoice_number": self.ref_invoice_number,
                "credit_note_number": self.bill_number,
                "credit_note_date": billed_date,
                "reason_for_return": self.reason_for_return
            })
        return result

    def _autosync_ird_report(self):
        unsynced_reports = self.search([('sync_with_ird', '=', False)])
        for report in unsynced_reports:
            report.sync_invoice_in_ird()

    def batch_ird_report_sync(self):
        config = self.env['res.config.settings']
        config_vals = config.get_values()
        if (config_vals['ird_realtime_data_sync']):
            for report in self:
                if report.sync_with_ird == False:
                    report.sync_invoice_in_ird()