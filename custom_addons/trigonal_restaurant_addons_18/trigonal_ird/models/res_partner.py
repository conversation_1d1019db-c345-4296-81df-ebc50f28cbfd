import logging
from odoo import api, fields, models, _
from odoo.exceptions import ValidationError

_logger = logging.getLogger(__name__)


class ResPartner(models.Model):
    _inherit = 'res.partner'

    @api.onchange('vat')
    def _valid_pan(self):
        if self.vat:
            if (len(self.vat) == 0 or len(self.vat) == 9) and self.vat[0:].isdigit() == True:
                pass
            else:
                raise ValidationError(_('Invalid Pan Number'))
