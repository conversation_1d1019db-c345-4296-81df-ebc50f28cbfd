import logging
import re
from datetime import date, datetime, timedelta

import nepali_datetime

from odoo import api, fields, models
from odoo.tools.translate import _
import pytz

# import datetime

log = logging.getLogger(__name__)


class AccountMove(models.Model):
    _inherit = 'account.move'

    # def current_user_datetime(self):
    #     currenttimezone = pytz.timezone(self.env.context.get('tz'))
    #     user_datetime = datetime.datetime.now(currenttimezone)
    #     self.timefield = user_datetime

    def convert_date_to_user_timezone(self, date_to_convert):
        # Get the user's timezone from the context or user settings
        user_timezone = self.env.context.get('tz') or self.env.user.tz or 'UTC'

        # Convert the date to the user's timezone
        try:
            utc_date = fields.Datetime.from_string(date_to_convert)  # Convert to datetime object
            user_tz = pytz.timezone(user_timezone)
            user_datetime = utc_date.astimezone(user_tz)
            return user_datetime
        except Exception as e:
            log.error(f"Error converting date: {e}")
            return date_to_convert  # Fallback to original if error occurs

    invoice_date = fields.Date(
        string='Invoice/Bill Date',
        index=True,
        copy=False,
        default=lambda self: date.today() + timedelta(hours=5, minutes=45)
    )

    invoice_print_count = fields.Integer(
        default=0, string='Print Count', help="Number of times invoice for this account move is printed.", store=True)
    bill_post = fields.Boolean(
        string='Sync state', copy=False, track_visibility='always', default=False)
    bill_data = fields.Char(string='Bill Data')
    last_printed = fields.Datetime(string='Last Printed', default=lambda self: fields.datetime.now(),
                                   track_visibility='always')
    ird_integ = fields.Boolean(
        'Connect to IRD', compute='_perform_compute_action')
    is_realtime = fields.Boolean('Is Realtime', default=False, stored=True)

    def _get_pos_receipt_filename(self):
        type_string = 'Invoice'
        invoice_numbers = self.name or ''
        if self.move_type in ('in_refund', 'out_refund'):
            type_string = 'Credit_Note'
        filename = '-'.join((
            type_string,
            invoice_numbers,
            self.company_id.display_name,
            self.partner_id.display_name or '')). \
            replace(' ', '-').replace(',', '').replace('--', '-')
        return filename

    @api.model
    def increase_print(self):
        if self.state == "posted":
            self.printed_count += 1

    def get_printedtime(self):
        return (datetime.now() + timedelta(hours=5, minutes=45)).strftime('%m/%d/%Y %H:%M:%S')

    def get_nepali_bill_date(self):
        if self.invoice_date:
            nepali_date = nepali_datetime.date.from_datetime_date(
                self.invoice_date)
            sp_date = str(nepali_date).split("-")
            return sp_date[1] + "/" + sp_date[2] + "/" + sp_date[0]
        return ""

    def get_printed_by(self):
        return self.env.user.name

    def get_accounting_date(self):
        if self.date:
            nepali_date = nepali_datetime.date.from_datetime_date(
                self.date)
            sp_date = str(nepali_date).split("-")
            return sp_date[1] + "/" + sp_date[2] + "/" + sp_date[0]
        return ""

    def action_post(self):
        result = super(AccountMove, self).action_post()

        config = self.env['res.config.settings']
        vals = config.get_values()
        if (self.move_type == 'out_invoice' or self.move_type == 'out_refund'):
            ird_report = self._update_ird_report(vals['ird_realtime_data_sync'])

            # if(self.payment_type == 'ONLINE'):
            #     self._handle_vat_return()

            if vals['ird_realtime_data_sync']:
                # with concurrent.futures.ThreadPoolExecutor() as executor:
                #     executor.submit(ird_report.sync_invoice_in_ird)
                notification = ird_report.sync_invoice_in_ird()
                self.ird_synced = notification['sync_with_ird']

            # Synchronize sales register
            sale_register = self.update_sale_register()

        elif self.move_type == 'in_invoice' or self.move_type == 'in_refund':
            # Synchronize purchase register
            purchase_register = self.update_purchase_register()

        return result

    def _update_ird_report(self, is_realtime_sync):
        taxable_discount = 0
        non_taxable_discount = 0
        taxable_subtotal = 0
        subtotal_without_tax = 0

        for line in self.invoice_line_ids:
            if len(line.tax_ids) > 0:
                taxable_subtotal = taxable_subtotal + line.quantity * line.price_unit
                taxable_discount = taxable_discount + line.quantity * line.price_unit * line.discount / 100
            if len(line.tax_ids) == 0:
                subtotal_without_tax = subtotal_without_tax + line.quantity * line.price_unit
                non_taxable_discount = non_taxable_discount + line.quantity * line.price_unit * line.discount / 100
        subtotal = taxable_subtotal + subtotal_without_tax
        taxable_amount = taxable_subtotal - taxable_discount
        tax_amount = taxable_amount * 0.13
        total_amount = taxable_amount + subtotal_without_tax - non_taxable_discount + tax_amount
        payload = {
            "fiscal_year": self.company_id.fiscal_year_name,
            "bill_number": self.name,
            "customer_name": self.commercial_partner_id.name,
            "customer_pan": self.commercial_partner_id.vat,
            "bill_date": self.invoice_date,
            "amount": subtotal,
            "discount": taxable_discount + non_taxable_discount,
            "taxable_amount": taxable_amount,
            "tax_amount": tax_amount,
            "total_amount": total_amount,
            "sync_with_ird": False,
            "is_bill_printed": False,
            "is_bill_active": True,
            "entered_by": self.invoice_user_id.name,
            "printed_by": '',
            "is_realtime": is_realtime_sync,
            "payment_method": '',
            "vat_refund_amount": taxable_amount * .1,
            "transaction_id": self.id,
            "type": self.move_type,
            "ref_invoice_number": self.reversed_entry_id.name if self.reversed_entry_id else '',
            "reason_for_return": self.ref
        }

        billed_date = nepali_datetime.date.from_datetime_date(self.invoice_date).strftime("%Y-%m-%d")
        payload.update({
            "invoice_date": billed_date
        })

        return self.env['trigonal.ird.report'].create(payload)
