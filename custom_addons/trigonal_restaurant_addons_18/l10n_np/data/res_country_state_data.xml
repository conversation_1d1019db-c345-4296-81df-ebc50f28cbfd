<?xml version="1.0" encoding="utf-8" ?>

<odoo>
    <data noupdate="0">
        <record id="provience_one_record" model="res.country.state">
            <field name="name">Koshi Province</field>
            <field name="code">NP-P1</field>
            <field name="country_id" ref="base.np"/>
        </record>
        <record id="provience_two_record" model="res.country.state">
            <field name="name">Madhesh Province</field>
            <field name="code">NP-P2</field>
            <field name="country_id" ref="base.np"/>
        </record>
        <record id="provience_three_record" model="res.country.state">
            <field name="name">Bagmati Province</field>
            <field name="code">NP-P3</field>
            <field name="country_id" ref="base.np"/>
        </record>
        <record id="provience_four_record" model="res.country.state">
            <field name="name">Gandaki Province</field>
            <field name="code">NP-P4</field>
            <field name="country_id" ref="base.np"/>
        </record>
        <record id="provience_five_record" model="res.country.state">
            <field name="name">Lumbini Province</field>
            <field name="code">NP-P5</field>
            <field name="country_id" ref="base.np"/>
        </record>
        <record id="provience_six_record" model="res.country.state">
            <field name="name">Karnali Province</field>
            <field name="code">NP-P6</field>
            <field name="country_id" ref="base.np"/>
        </record>
        <record id="provience_seven_record" model="res.country.state">
            <field name="name">Sudurpashchim Province</field>
            <field name="code">NP-P7</field>
            <field name="country_id" ref="base.np"/>
        </record>
    </data>
</odoo>
