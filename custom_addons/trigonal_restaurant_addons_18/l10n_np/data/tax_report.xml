<odoo>
    <record id="account_report_nepal_vat" model="account.report">
        <field name="name">Nepal VAT Report</field>
        <field name="root_report_id" eval="False"/>
        <field name="main_report_id" eval="False"/>
        <field name="line_ids" eval="[(5, 0, 0)]"/>
        <field name="country_id" ref="base.np"/>
        <field name="filter_date" eval="True"/>
        <field name="filter_journals" eval="True"/>
        <field name="filter_analytic" eval="False"/>
        <field name="column_ids" eval="[(5, 0, 0)]"/>
    </record>

    <!-- Columns -->
    <record id="account_report_nepal_vat_col_base" model="account.report.column">
        <field name="report_id" ref="l10n_np.account_report_nepal_vat"/>
        <field name="name">Base Amount</field>
        <field name="expression_label">base</field>
        <field name="figure_type">monetary</field>
    </record>

    <record id="account_report_nepal_vat_col_vat" model="account.report.column">
        <field name="report_id" ref="l10n_np.account_report_nepal_vat"/>
        <field name="name">VAT Amount</field>
        <field name="expression_label">vat</field>
        <field name="figure_type">monetary</field>
    </record>

    <!-- Sales Line -->
    <record id="account_report_nepal_vat_sales" model="account.report.line">
        <field name="report_id" ref="l10n_np.account_report_nepal_vat"/>
        <field name="name">Sales (13%)</field>
        <field name="code">sales</field>
        <field name="expression_ids" eval="[(0, 0, {
            'label': 'base',
            'engine': 'domain',
            'domain': [('tax_ids.amount', '=', 13.0), ('move_type', '=', 'out_invoice')],
            'date_scope': 'report',
            'figure_type': 'monetary'
        }), (0, 0, {
            'label': 'vat',
            'engine': 'domain',
            'domain': [('tax_ids.amount', '=', 13.0), ('move_type', '=', 'out_invoice')],
            'date_scope': 'report',
            'figure_type': 'monetary',
            'groupby': 'tax_ids'
        })]"/>
    </record>

    <!-- Purchase Line -->
    <record id="account_report_nepal_vat_purchase" model="account.report.line">
        <field name="report_id" ref="l10n_np.account_report_nepal_vat"/>
        <field name="name">Purchases (13%)</field>
        <field name="code">purchase</field>
        <field name="expression_ids" eval="[(0, 0, {
            'label': 'base',
            'engine': 'domain',
            'domain': [('tax_ids.amount', '=', 13.0), ('move_type', '=', 'in_invoice')],
            'date_scope': 'report',
            'figure_type': 'monetary'
        }), (0, 0, {
            'label': 'vat',
            'engine': 'domain',
            'domain': [('tax_ids.amount', '=', 13.0), ('move_type', '=', 'in_invoice')],
            'date_scope': 'report',
            'figure_type': 'monetary',
            'groupby': 'tax_ids'
        })]"/>
    </record>
</odoo>
