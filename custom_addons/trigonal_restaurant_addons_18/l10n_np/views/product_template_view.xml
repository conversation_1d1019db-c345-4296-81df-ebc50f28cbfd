<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record model="ir.ui.view" id="l10n_np.product_template_hsn_code">
        <field name="name">l10n_np.product.template.form.hsn_code</field>
        <field name="model">product.template</field>
        <field name="inherit_id" ref="product.product_template_form_view"/>
        <field name="arch" type="xml">
            <field name="categ_id" position="after">
                <field name="l10n_np_hs_code" invisible="'NP' not in fiscal_country_codes"/>
            </field>
        </field>
    </record>

    <record id="l10n_np.product_template_hsn_code_tree" model="ir.ui.view">
            <field name="name">l10n_np.product.template.tree.hsn_code</field>
            <field name="model">product.template</field>
            <field name="inherit_id" ref="product.product_template_tree_view"/>
            <field name="arch" type="xml">
                <field name="default_code" position="after">
                    <field name="l10n_np_hs_code" invisible="'NP' not in fiscal_country_codes" optional="show"/>
                </field>
            </field>
        </record>

</odoo>
