# Part of Odoo. See LICENSE file for full copyright and licensing details.
from odoo import models, fields, _
from odoo.addons.account.models.chart_template import template
from odoo import Command


class AccountChartTemplate(models.AbstractModel):
    _inherit = 'account.chart.template'

    @template('np')
    def _get_in_template_data(self):
        return {
            'property_account_receivable_id': 'p10040',
            'property_account_payable_id': 'p11211',
            'property_account_expense_categ_id': 'p2107',
            'property_account_income_categ_id': 'p20011',
            'code_digits': '6',
            'display_invoice_amount_total_words': True,
        }

    @template('np', 'res.company')
    def _get_in_res_company(self):
        return {
            self.env.company.id: {
                'account_fiscal_country_id': 'base.np',
                'bank_account_code_prefix': '1002',
                'cash_account_code_prefix': '1001',
                'transfer_account_code_prefix': '1008',
                'account_default_pos_receivable_account_id': 'p10041',
                'income_currency_exchange_account_id': 'p2013',
                'expense_currency_exchange_account_id': 'p2117',
                'account_journal_early_pay_discount_loss_account_id': 'p2132',
                'account_journal_early_pay_discount_gain_account_id': '2012',
                'account_opening_date': fields.Date.context_today(self).replace(month=7, day=16),
                'fiscalyear_last_month': '7',
                'account_sale_tax_id': 'vat_sale_13',
                'account_purchase_tax_id': 'vat_purchase_13',
                'deferred_expense_account_id': 'p10084',
                'deferred_revenue_account_id': 'p10085',
            },
        }

    @template('np', 'account.cash.rounding')
    def _get_in_account_cash_rounding(self):
        return {
            'l10n_np.cash_rounding_in_half_up': {
                'profit_account_id': 'p213202',
                'loss_account_id': 'p213201',
            }
        }
