from odoo import api, fields, models


class AccountMoveLine(models.Model):
    _inherit = "account.move.line"

    l10n_np_hs_code = fields.Char(string="HS Code", compute="_compute_hs_code", store=True, readonly=False, copy=False)

    @api.depends('product_id', 'product_id.l10n_np_hs_code')
    def _compute_hs_code(self):
        for line in self:
            if line.move_id.country_code == 'NP' and line.parent_state == 'draft':
                line.l10n_np_hs_code = line.product_id.l10n_np_hs_code
