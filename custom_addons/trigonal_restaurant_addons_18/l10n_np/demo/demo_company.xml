<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="base.partner_demo_company_np" model="res.partner" forcecreate="1">
        <field name="name">Nepali Company</field>
        <field name="vat">1234567</field>
        <field name="street">Singhadurbar</field>
        <field name="city">Kathmandu</field>
        <field name="country_id" ref="base.np"/>
        <field name="zip">44600</field>
        <field name="email"><EMAIL></field>
        <field name="website">www.thexample.com</field>
        <field name="is_company" eval="True"/>
    </record>

    <record id="base.demo_company_np" model="res.company" forcecreate="1">
        <field name="name">Nepali Company</field>
        <field name="partner_id" ref="base.partner_demo_company_np"/>
    </record>

    <function model="res.company" name="_onchange_country_id">
        <value eval="[ref('base.demo_company_np')]"/>
    </function>

    <function model="res.users" name="write">
        <value eval="[ref('base.user_root'), ref('base.user_admin'), ref('base.user_demo')]"/>
        <value eval="{'company_ids': [(4, ref('base.demo_company_np'))]}"/>
    </function>

    <function model="account.chart.template" name="try_loading">
        <value eval="[]"/>
        <value>tn</value>
        <value model="res.company" eval="obj().env.ref('base.demo_company_np')"/>
        <value name="install_demo" eval="True"/>
    </function>
</odoo>
