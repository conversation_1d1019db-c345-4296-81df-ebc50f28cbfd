<?xml version="1.0" encoding="UTF-8" ?>
<odoo>
    <!--    Room Booking Detail form view-->
    <record id="room_booking_detail_view_form" model="ir.ui.view">
        <field name="name">room.booking.detail.view.form</field>
        <field name="model">room.booking.detail</field>
        <field name="arch" type="xml">
            <form name="report">
                <group col="4">
                    <field name="checkin"/>
                    <field name="checkout"/>
                    <field name="room_id"
                           options="{'no_create': True, 'no_quick_create': True,
                           'no_create_edit':True}"/>
                </group>
                <footer>
                    <button name="action_room_booking_pdf" type="object"
                            class="btn-primary">
                        <i class="fa fa-download mr-2"/>
                        Download PDF
                    </button>
                    <button name="action_room_booking_excel" type="object"
                            class="btn-primary">
                        <i class="fa fa-download mr-2"/>
                        Download XLS
                    </button>
                    <button special="cancel" string="Cancel"
                            class="btn-secondary"/>
                </footer>
            </form>
        </field>
    </record>
    <!--    Room Booking Report Menu Action-->
    <record id="room_booking_detail_action" model="ir.actions.act_window">
        <field name="name">Room Booking Report</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">room.booking.detail</field>
        <field name="view_mode">form</field>
        <field name="view_id" ref="room_booking_detail_view_form"/>
        <field name="target">new</field>
    </record>
</odoo>
