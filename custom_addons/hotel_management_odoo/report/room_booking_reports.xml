<?xml version = "1.0" encoding = "UTF-8" ?>
<odoo>
    <!--    Room booking report action-->
    <record id="action_report_room_booking" model="ir.actions.report">
        <field name="name">Room Booking Order</field>
        <field name="model">room.booking</field>
        <field name="report_type">qweb-pdf</field>
        <field name="report_name">hotel_management_odoo.report_room_booking</field>
        <field name="report_file">hotel_management_odoo.report_room_booking</field>
        <field name="binding_model_id" ref="model_room_booking"/>
        <field name="binding_type">report</field>
    </record>
    <!--    Room booking template-->
    <template id="report_room_booking">
        <t t-call="web.html_container">
            <t t-call="web.external_layout">
                <div class="page">
                    <h2>Room Booking</h2>
                    <br/>
                    <table class="table">
                        <thead>
                            <tr>
                                <th>Sl No.</th>
                                <th>Guest Name</th>
                                <th>Room No.</th>
                                <th>Check-In</th>
                                <th>Check-Out</th>
                                <th>Reference No.</th>
                            </tr>
                        </thead>
                        <t t-set="i" t-value="0"/>
                        <t t-foreach="booking" t-as="line">
                            <t t-set="i" t-value="i+1"/>
                            <tr>
                                <td>
                                    <t t-esc="i"/>
                                </td>
                                <td>
                                    <t t-esc="line['partner_id']"/>
                                </td>
                                <td>
                                    <t t-esc="line['room']"/>
                                </td>
                                <td>
                                    <t t-esc="line['checkin_date']"/>
                                </td>
                                <td>
                                    <t t-esc="line['checkin_date']"/>
                                </td>
                                <td>
                                    <t t-esc="line['name']"/>
                                </td>
                            </tr>
                        </t>
                    </table>
                </div>
            </t>
        </t>
    </template>
</odoo>
