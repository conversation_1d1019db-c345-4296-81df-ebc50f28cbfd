<?xml version="1.0" encoding="UTF-8" ?>
<odoo>
    <!--   Inherited  Fleet Vehicle form view to add extra fields  -->
    <record id="fleet_vehicle_model_view_form" model="ir.ui.view">
        <field name="name">
            fleet.vehicle.model.view.form.inherit.odoo.hotel.management
        </field>
        <field name="model">fleet.vehicle.model</field>
        <field name="inherit_id" ref="fleet.fleet_vehicle_model_view_form"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='model_year']" position="after">
                <field name="price_per_km" widget="monetary"/>
                <field name="uom_id"/>
            </xpath>
        </field>
    </record>
    <!-- Vehicles Menu action -->
    <record id="fleet_vehicle_model_action" model="ir.actions.act_window">
        <field name="name">Vehicles</field>
        <field name="res_model">fleet.vehicle.model</field>
        <field name="view_mode">list,form</field>
        <field name="help" type="html">
            <p class="oe_view_nocontent_create">
                No Vehicles found ! Let's create one
            </p>
        </field>
    </record>
    <!-- Manufacturer Menu action -->
    <record id="fleet_vehicle_model_brand_action"
            model="ir.actions.act_window">
        <field name="name">Manufacturers</field>
        <field name="res_model">fleet.vehicle.model.brand</field>
        <field name="view_mode">kanban,list,form</field>
        <field name="help" type="html">
            <p class="oe_view_nocontent_create">
                No Manufacturers found ! Let's create one
            </p>
        </field>
    </record>
    <!-- Vehicles Menu  -->
    <menuitem id="fleet_vehicle_model_menu" name="Vehicles"
              action="fleet_vehicle_model_action" parent="fleet_vehicle_menu"
              sequence="10"/>
    <!--     Manufacturer Menu -->
    <menuitem id="fleet_vehicle_model_brand_menu" name="Manufacturers"
              action="fleet_vehicle_model_brand_action"
              parent="fleet_vehicle_menu" sequence="20"/>
</odoo>
