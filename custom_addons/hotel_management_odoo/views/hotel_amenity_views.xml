<?xml version="1.0" encoding="UTF-8"?>
<odoo>
    <!--    Hotel Amenity Tree View-->
    <record id="hotel_amenity_view_tree" model="ir.ui.view">
        <field name="name">hotel.amenity.view.tree</field>
        <field name="model">hotel.amenity</field>
        <field name="arch" type="xml">
            <list>
                <field name="name"/>
            </list>
        </field>
    </record>
    <!--    Hotel Amenity Form View-->
    <record id="hotel_amenity_view_form" model="ir.ui.view">
        <field name="name">hotel.amenity.view.form</field>
        <field name="model">hotel.amenity</field>
        <field name="arch" type="xml">
            <form string="Amenity">
                <sheet>
                    <group>
                        <group>
                            <field name="name"/>
                            <field name="description"/>
                        </group>
                        <group>
                            <field name="icon" widget="image" class="oe_avatar"
                                   string=""/>
                        </group>
                    </group>
                </sheet>
                <chatter/>
            </form>
        </field>
    </record>
    <!--    Amenity Menu Action-->
    <record id="hotel_amenity_action" model="ir.actions.act_window">
        <field name="name">Amenity</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">hotel.amenity</field>
        <field name="view_mode">list,form</field>
        <field name="context">{'default_detailed_type': 'service'}</field>
        <field name="help" type="html">
            <p class="oe_view_nocontent_create">
                No Amenity found ! Let's create one
            </p>
        </field>
    </record>
    <!--Amenity Menu-->
    <menuitem id="hotel_amenity_menu"
              name="Amenity"
              action="hotel_amenity_action"
              parent="hotel_config_menu"
              sequence="30"/>
</odoo>
