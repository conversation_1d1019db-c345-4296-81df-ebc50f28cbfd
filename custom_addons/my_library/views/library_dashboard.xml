<?xml version="1.0" encoding="UTF-8"?>
<odoo>
    <record id="library_dashboard_view_kanban" model="ir.ui.view">
        <field name="name">library.dashboard.kanban</field>
        <field name="model">library.book</field>
        <field name="arch" type="xml">
            <kanban class="oe_background_grey o_kanban_dashboard" create="false">
                <field name="name"/>
                <field name="state"/>
                <templates>
                    <t t-name="kanban-box">
                        <div class="oe_kanban_global_click container">
                            <div class="row">
                                <!-- Total Books Card -->
                                <div class="col-4">
                                    <div class="card bg-info text-white mb-3">
                                        <div class="card-body">
                                            <h5 class="card-title">Total Books</h5>
                                            <p class="card-text display-4" t-esc="record.total_books"/>
                                        </div>
                                    </div>
                                </div>
                                <!-- Available Books Card -->
                                <div class="col-4">
                                    <div class="card bg-success text-white mb-3">
                                        <div class="card-body">
                                            <h5 class="card-title">Available Books</h5>
                                            <p class="card-text display-4" t-esc="record.available_books"/>
                                        </div>
                                    </div>
                                </div>
                                <!-- Checked Out Books Card -->
                                <div class="col-4">
                                    <div class="card bg-warning text-white mb-3">
                                        <div class="card-body">
                                            <h5 class="card-title">Checked Out</h5>
                                            <p class="card-text display-4" t-esc="record.checked_out_books"/>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row mt-3">
                                <!-- Total Members Card -->
                                <div class="col-4">
                                    <div class="card bg-primary text-white mb-3">
                                        <div class="card-body">
                                            <h5 class="card-title">Total Members</h5>
                                            <p class="card-text display-4" t-esc="record.total_members"/>
                                        </div>
                                    </div>
                                </div>
                                <!-- Active Members Card -->
                                <div class="col-4">
                                    <div class="card bg-secondary text-white mb-3">
                                        <div class="card-body">
                                            <h5 class="card-title">Active Members</h5>
                                            <p class="card-text display-4" t-esc="record.active_members"/>
                                        </div>
                                    </div>
                                </div>
                                <!-- Total Checkouts Card -->
                                <div class="col-4">
                                    <div class="card bg-danger text-white mb-3">
                                        <div class="card-body">
                                            <h5 class="card-title">Monthly Checkouts</h5>
                                            <p class="card-text display-4" t-esc="record.monthly_checkouts"/>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </t>
                </templates>
            </kanban>
        </field>
    </record>

    <!-- now i wnat a thang man -->



    <!-- Dashboard Action -->
    <record id="action_library_dashboard" model="ir.actions.act_window">
        <field name="name">Library Dashboard</field>
        <field name="res_model">library.book</field>
        <field name="view_mode">kanban</field>
        <field name="view_id" ref="library_dashboard_view_kanban"/>
        <field name="domain">[]</field>
        <field name="context">{}</field>
    </record>

    <!-- Dashboard Menu Item -->
    <menuitem
        id="menu_library_dashboard"
        name="Dashboard"
        parent="library_menu_root"
        action="action_library_dashboard"
        sequence="1"/>
</odoo> 