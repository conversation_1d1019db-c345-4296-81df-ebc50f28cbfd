from datetime import timedelta, datetime
import logging
from secrets import choice

from odoo import api, fields, models, _
from odoo.exceptions import ValidationError
from odoo.tools.image import image_data_uri

_logger = logging.getLogger(__name__)


class PosPayment(models.Model):
    _inherit = 'pos.payment'

    # payment_method_id.qr_code_method
    # generated by Payment method ----------------
    qr30_ref1 = fields.Char(string="Reference 1", help="The internal reference of the transaction")
    qr30_ref2 = fields.Char(string="Reference 2", help="The internal reference of the transaction")
    qr30_ref3 = fields.Char(string="Reference 3", help="The internal reference of the transaction")

    # await payment -----------------------
    qr30_raw_data = fields.Text('QR raw data')
    qr30_image = fields.Text('QR image data', store=False)
    qr30_expire_time = fields.Datetime(store=False)

    # post payment ------------------------
    # transaction
    # transaction_id from parent
    qr30_transaction_type = fields.Char(string="Transaction Type")
    qr30_transaction_time = fields.Datetime(
        default=lambda self: fields.Datetime.now(),
        string="Transaction Time")
    # customer
    qr30_payer_bank = fields.Selection(
        selection=lambda self: self.env['pos.bank'].get_bank_code(),
        string="Bank")
    qr30_payer_account_number = fields.Char(string="Acc No")
    qr30_payer_account_name = fields.Char(string="Acc Name")

    # for view ---------------------------
    qr_code_method = fields.Selection(
        string="QR Method Name", related='payment_method_id.qr_code_method', store=False)

    # @api.model
    # def _load_pos_data_fields(self, config_id):
    #     return ['id', 'amount', 'currency_id', 'qr30_raw_data', 'qr30_expire_time', 'transaction_id', 'qr30_ref1',
    #             'qr30_ref2', 'qr30_ref3', 'qr30_payer_bank', 'qr30_payer_account_number', 'qr30_payer_account_name']

    def _send_refund_request(self, amount_to_refund=None):
        print("\n\n Call Action Refund Payment Button >>>>> ", self)

    def _send_capture_request(self, amount_to_capture=None):
        pass
