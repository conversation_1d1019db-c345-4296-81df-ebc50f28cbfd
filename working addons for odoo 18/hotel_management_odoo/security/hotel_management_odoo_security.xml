<?xml version="1.0" encoding="UTF-8" ?>
<odoo>
    <!--  MAINTENANCE RECORD RULE-->
    <record id="maintenance_request_rule_leader"
            model="ir.rule">
        <field name="name">Record Rule for Maintenance team leader</field>
        <field ref="model_maintenance_request" name="model_id"/>
        <field name="groups"
               eval="[(4, ref('hotel_management_odoo.maintenance_team_group_leader'))]"/>
        <field name="domain_force">[('team_head_id', '=', user.id)]</field>
        <field name="perm_read" eval="True"/>
        <field name="perm_write" eval="True"/>
        <field name="perm_create" eval="True"/>
        <field name="perm_unlink" eval="True"/>
    </record>
    <!--    Record Rule for Maintenance User-->
    <record id="maintenance_request_rule_user"
            model="ir.rule">
        <field name="name">Record Rule for Maintenance User</field>
        <field ref="model_maintenance_request" name="model_id"/>
        <field name="groups"
               eval="[(4, ref('hotel_management_odoo.maintenance_team_group_user'))]"/>
        <field name="domain_force">[('assigned_user_id', '=', user.id)]</field>
        <field name="perm_read" eval="True"/>
        <field name="perm_write" eval="True"/>
        <field name="perm_create" eval="True"/>
        <field name="perm_unlink" eval="True"/>
    </record>
    <!--    Record Rule for hotel Admin-->
    <record id="maintenance_request_rule_admin"
            model="ir.rule">
        <field name="name">Record Rule for hotel Admin</field>
        <field ref="model_maintenance_request" name="model_id"/>
        <field name="groups"
               eval="[(4, ref('hotel_management_odoo.hotel_group_admin'))]"/>
        <field name="domain_force">[(1,'=',1)]</field>
        <field name="perm_read" eval="True"/>
        <field name="perm_write" eval="True"/>
        <field name="perm_create" eval="True"/>
        <field name="perm_unlink" eval="True"/>
    </record>
    <!--    CLEANING HEAD RECORD RULE-->
    <record id="cleaning_request_rule_head" model="ir.rule">
        <field name="name">Cleaning Head Record Rule</field>
        <field name="model_id" ref="model_cleaning_request"/>
        <field name="groups"
               eval="[(4, ref('hotel_management_odoo.cleaning_team_group_head'))]"/>
        <field name="domain_force">[('team_id.team_head_id.id', '=', user.id)]
        </field>
    </record>
    <!--    CLEANING USER RECORD RULE-->
    <record id="cleaning_request_rule_user" model="ir.rule">
        <field name="name">Cleaning User Record Rule</field>
        <field name="model_id" ref="model_cleaning_request"/>
        <field name="groups"
               eval="[(4, ref('hotel_management_odoo.cleaning_team_group_user'))]"/>
        <field name="domain_force">[('assigned_id.id', '=', user.id)]</field>
    </record>
    <!--    CLEANING ADMIN RECORD RULE-->
    <record id="cleaning_request_rule_admin" model="ir.rule">
        <field name="name">Cleaning Admin Record Rule</field>
        <field name="model_id" ref="model_cleaning_request"/>
        <field name="groups"
               eval="[(4, ref('hotel_management_odoo.hotel_group_admin'))]"/>
        <field name="domain_force">[(1, '=', 1)]</field>
    </record>
</odoo>
