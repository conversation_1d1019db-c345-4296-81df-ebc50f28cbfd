<?xml version="1.0" encoding="UTF-8" ?>
<odoo>
    <!--    Hotel Access Groups-->
    <record id="module_category_hotel_management" model="ir.module.category">
        <field name="name">Hotel Management</field>
        <field name="description">Hotel Access Groups</field>
        <field name="sequence">20</field>
    </record>
    <!--    Maintenance Team User groups-->
    <record id="maintenance_team_group_user" model="res.groups">
        <field name="name">Maintenance Team User</field>
        <field name="category_id"
               ref="hotel_management_odoo.module_category_hotel_management"/>
    </record>
    <!--    Maintenance Team Leader group-->
    <record id="maintenance_team_group_leader" model="res.groups">
        <field name="name">Maintenance Team Leader</field>
        <field name="category_id"
               ref="hotel_management_odoo.module_category_hotel_management"/>
        <field name="implied_ids"
               eval="[(4, ref('hotel_management_odoo.maintenance_team_group_user'))]"/>
    </record>
    <!--    Reception groups-->
    <record id="hotel_group_reception" model="res.groups">
        <field name="name">Receptionist</field>
        <field name="category_id"
               ref="hotel_management_odoo.module_category_hotel_management"/>
    </record>
    <!--    Cleaning Team User group-->
    <record id="cleaning_team_group_user" model="res.groups">
        <field name="name">Cleaning Team User</field>
        <field name="category_id"
               ref="hotel_management_odoo.module_category_hotel_management"/>
    </record>
    <!--    Cleaning Team Head group-->
    <record id="cleaning_team_group_head" model="res.groups">
        <field name="name">Cleaning Team Head</field>
        <field name="category_id"
               ref="hotel_management_odoo.module_category_hotel_management"/>
        <field name="implied_ids"
               eval="[(4, ref('hotel_management_odoo.cleaning_team_group_user'))]"/>
    </record>
    <!--Hotel Admin group-->
    <record id="hotel_group_admin" model="res.groups">
        <field name="name">Admin</field>
        <field name="category_id"
               ref="hotel_management_odoo.module_category_hotel_management"/>
        <field name="implied_ids"
               eval="[(4, ref('hotel_management_odoo.maintenance_team_group_leader')),
                       (4, ref('hotel_management_odoo.cleaning_team_group_head')),
                       (4, ref('hotel_group_reception')), ]"/>
    </record>
</odoo>
