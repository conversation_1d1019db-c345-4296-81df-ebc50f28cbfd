<?xml version="1.0" encoding="UTF-8" ?>
<!--Dashboard template-->
<template>
    <t t-name="CustomDashBoard">
        <div class="oh_dashboards"
             style="margin-top: 20px;">
            <div class="container-fluid o_pj_dashboard">
                <t t-call="HotelOrder"/>
            </div>
        </div>
    </t>
    <t t-name="HotelOrder">
        <div class="row main-section">
            <!--            Total Room-->
            <div class="col-md-3 col-sm-6 total_room dash-card"
                 t-on-click="(e) => this.total_rooms(e)">
                <div class="card" style="width: 18rem;">
                    <div class="card-img-top dash-icon-1 "
                         style="color:#e39db5;">
                        <i class="fa fa-bed fa-lg"/>
                    </div>
                    <div class="card-body">
                        <div class="dash-count">
                            <t t-esc="total_room"/>
                        </div>
                        <div class="dash-head">Total Room</div>
                    </div>
                </div>
            </div>
            <!-- Check In-->
            <div groups="hotel_management_odoo.hotel_group_reception"
                 class="col-md-3 col-sm-6 check_in dash-card"
                 t-on-click="(e) => this.check_ins(e)">
                <div class="card" style="width: 18rem;">
                    <div class="card-img-top dash-icon-1"
                         style="color:#87c773;">
                        <i class="fa fa-sign-in fa-lg"/>
                    </div>
                    <div class="card-body">
                        <div class="dash-count">
                            <t t-esc="check_in"/>
                        </div>
                        <div class="dash-head">Check In</div>
                    </div>
                </div>
            </div>
            <!--            Available Room-->
            <div groups="hotel_management_odoo.hotel_group_reception"
                 class="col-md-3 col-sm-6 available_room dash-card"
                 t-on-click="(e)=> this.available_rooms(e)">
                <div class="card" style="width: 18rem;">
                    <div class="card-img-top dash-icon-1"
                         style="color:#3858d9;">
                        <i class="fa fa-check-circle fa-lg"/>
                    </div>
                    <div class="card-body">
                        <div class="dash-count">
                            <t t-esc="available_room"/>
                        </div>
                        <div class="dash-head">Available Rooms</div>
                    </div>
                </div>
            </div>
            <!--            Total Reservations-->
            <div groups="hotel_management_odoo.hotel_group_reception"
                 class="col-md-3 col-sm-6 reservations dash-card"
                 t-on-click="(e)=> this.reservations(e)">
                <div class="card" style="width: 18rem;">
                    <div class="card-img-top dash-icon-1"
                         style="color:#384d2f;">
                        <i class="fa fa-bookmark"/>
                    </div>
                    <div class="card-body">
                        <div class="dash-count">
                            <t t-esc="reservation"/>
                        </div>
                        <div class="dash-head">Total Reservations</div>
                    </div>
                </div>
            </div>
            <!-- Today's Check Out-->
            <div groups="hotel_management_odoo.hotel_group_reception"
                 class="col-md-3 col-sm-6 today_check_out dash-card"
                 t-on-click="()=>this.check_outs(e)">
                <div class="card" style="width: 18rem;">
                    <div class="card-img-top dash-icon-1"
                         style="color:#e07407;">
                        <i class="fa fa-sign-out fa-lg"/>
                    </div>
                    <div class="card-body">
                        <div class="dash-count">
                            <t t-esc="check_out"/>
                        </div>
                        <div class="dash-head">Today's Check Out</div>
                    </div>
                </div>
            </div>
            <!-- Total Staff-->
            <div groups="hotel_management_odoo.hotel_group_reception"
                 class="col-md-3 col-sm-6 total_staff dash-card"
                 t-on-click="(e) => this.fetch_total_staff(e)">
                <div class="card" style="width: 18rem;">
                    <div class="card-img-top dash-icon-1"
                         style="color:#6e2940;">
                        <i class="fa fa-group fa-lg"/>
                    </div>
                    <div class="card-body">
                        <div class="dash-count">
                            <t t-esc="staff"/>
                        </div>
                        <div class="dash-head">Total Staff</div>
                    </div>
                </div>
            </div>
            <!--            Total Vehicle-->
            <div groups="hotel_management_odoo.hotel_group_reception"
                 class="col-md-3 col-sm-6 total_vehicle dash-card"
                 t-on-click="(e)=> this.fetch_total_vehicle(e)">
                <div class="card" style="width: 18rem;">
                    <div class="card-img-top dash-icon-1"
                         style="color:#f5f39f;">
                        <i class="fa fa-car fa-lg"/>
                    </div>
                    <div class="card-body">
                        <div class="dash-count">
                            <t t-esc="total_vehicle"/>
                        </div>
                        <div class="dash-head">Total Vehicle</div>
                    </div>
                </div>
            </div>
            <!--            Available Vehicle-->
            <div groups="hotel_management_odoo.hotel_group_reception"
                 class="col-md-3 col-sm-6 available_vehicle dash-card"
                 t-on-click="(e)=> this.fetch_available_vehicle(e)">
                <div class="card" style="width: 18rem;">
                    <div class="card-img-top dash-icon-1"
                         style="color:#269138;">
                        <i class="fa fa-car fa-lg"/>
                    </div>
                    <div class="card-body">
                        <div class="dash-count">
                            <t t-esc="available_vehicle"/>
                        </div>
                        <div class="dash-head">Available Vehicle</div>
                    </div>
                </div>
            </div>
            <!--            Total Events-->
            <div groups="hotel_management_odoo.hotel_group_reception"
                 class="col-md-3 col-sm-6 total_events dash-card"
                 t-on-click="(e)=> this.view_total_events(e)">
                <div class="card" style="width: 18rem;">
                    <div class="card-img-top dash-icon-1"
                         style="color:#b0abdb;">
                        <i class="fa fa-ticket fa-lg"/>
                    </div>
                    <div class="card-body">
                        <div class="dash-count">
                            <t t-esc="total_event"/>
                        </div>
                        <div class="dash-head">Total Events</div>
                    </div>
                </div>
            </div>
            <!--            Today's Events-->
            <div groups="hotel_management_odoo.hotel_group_reception"
                 class="col-md-3 col-sm-6 today_events dash-card"
                 t-on-click="(e) => this.fetch_today_events(e)">
                <div class="card" style="width: 18rem;">
                    <div class="card-img-top dash-icon-1"
                         style="color:#d9691a;">
                        <i class="fa fa-calendar fa-lg"/>
                    </div>
                    <div class="card-body">
                        <div class="dash-count">
                            <t t-esc="today_events"/>
                        </div>
                        <div class="dash-head">Today's Events</div>
                    </div>
                </div>
            </div>
            <!--            Pending Events-->
            <div groups="hotel_management_odoo.hotel_group_reception"
                 class="col-md-3 col-sm-6 pending_event dash-card"
                 t-on-click="(e) => this.fetch_pending_events(e)">
                <div class="card" style="width: 18rem;">
                    <div class="card-img-top dash-icon-1"
                         style="color:#e3372b;">
                        <i class="fa fa-exclamation-triangle fa-lg"/>
                    </div>
                    <div class="card-body">
                        <div class="dash-count">
                            <t t-esc="pending_events"/>
                        </div>
                        <div class="dash-head">Pending Events</div>
                    </div>
                </div>
            </div>
            <!--            Food Items-->
            <div groups="hotel_management_odoo.hotel_group_reception"
                 class="col-md-3 col-sm-6 food_item dash-card"
                 t-on-click="(e)=> this.fetch_food_item(e)">
                <div class="card" style="width: 18rem;">
                    <div class="card-img-top dash-icon-1"
                         style="color:#d95f14;">
                        <i class="fa fa-cutlery fa-lg"/>
                    </div>
                    <div class="card-body">
                        <div class="dash-count">
                            <t t-esc="food_items"/>
                        </div>
                        <div class="dash-head">Food Items</div>
                    </div>
                </div>
            </div>
            <!--            Food Orders-->
            <div groups="hotel_management_odoo.hotel_group_reception"
                 class="col-md-3 col-sm-6 food_order dash-card"
                 t-on-click="(e)=>this.fetch_food_order(e)">
                <div class="card" style="width: 18rem;">
                    <div class="card-img-top dash-icon-1"
                         style="color:#f5cc02;">
                        <i class="fa fa-cutlery fa-lg"/>
                    </div>
                    <div class="card-body">
                        <div class="dash-count">
                            <t t-esc="food_order"/>
                        </div>
                        <div class="dash-head">Food Orders</div>
                    </div>
                </div>
            </div>
            <!--            Total Revenue-->
            <div groups="hotel_management_odoo.hotel_group_reception"
                 class="col-md-3 col-sm-6 total_revenue dash-card">
                <div class="card" style="width: 18rem;">
                    <div class="card-img-top dash-icon-1"
                         style="color:#666660">
                        <i class="fa fa-usd fa-lg"/>
                    </div>
                    <div class="card-body">
                        <div class="dash-count"
                             style="font-size:30px;margin-bottom:20px;">
                            <t t-esc="total_revenue"/>
                        </div>
                        <div class="dash-head">Total Revenue</div>
                    </div>
                </div>
            </div>
            <!--            Today Revenue-->
            <div groups="hotel_management_odoo.hotel_group_reception"
                 class="col-md-3 col-sm-6 today_revenue dash-card">
                <div class="card" style="width: 18rem;">
                    <div class="card-img-top dash-icon-1"
                         style="color:#397303;">
                        <i class="fa fa-money fa-lg"/>
                    </div>
                    <div class="card-body">
                        <div class="dash-count"
                             style="font-size:30px;margin-bottom:20px;">
                            <t t-esc="today_revenue"/>
                        </div>
                        <div class="dash-head">Today's Revenue</div>
                    </div>
                </div>
            </div>
            <!--            Pending Payment-->
            <div groups="hotel_management_odoo.hotel_group_reception"
                 class="col-md-3 col-sm-6 pending_payment dash-card">
                <div class="card" style="width: 18rem;">
                    <div class="card-img-top dash-icon-1"
                         style="color:#f0b686;">
                        <i class="fa fa-credit-card"/>
                    </div>
                    <div class="card-body">
                        <div class="dash-count"
                             style="font-size:30px;margin-bottom:20px;">
                            <t t-esc="pending_payment"/>
                        </div>
                        <div class="dash-head">Pending Payment</div>
                    </div>
                </div>
            </div>
        </div>
    </t>
</template>
