<?xml version = "1.0" encoding = "UTF-8" ?>
<odoo>
    <!--    Sale order report action-->
    <record id="action_report_sale_order" model="ir.actions.report">
   <field name="name">Sale Order</field>
   <field name="model">room.booking</field>
   <field name="report_type">qweb-pdf</field>
   <field name="report_name">hotel_management_odoo.report_sale_order</field>
   <field name="report_file">hotel_management_odoo.report_sale_order</field>
   <field name="binding_model_id" ref="model_room_booking"/>
   <field name="binding_type">report</field>
</record>
    <!--    Sale order report template-->
    <template id="report_sale_order">
        <t t-call="web.html_container">
            <t t-call="web.external_layout">
                <div class="page">
                    <div class="oe_structure"/>
                    <h2>Sale Order</h2>
                    <br/>
                    <table class="table">
                        <thead>
                            <tr>
                                <th>Sl No.</th>
                                <th>Guest Name</th>
                                <th>Check-In</th>
                                <th>Check-Out</th>
                                <th>Reference No.</th>
                                <th>Amount Total</th>
                            </tr>
                        </thead>
                        <t t-set="i" t-value="0"/>
                        <t t-foreach="booking" t-as="line">
                            <t t-set="i" t-value="i+1"/>
                            <tr>
                                <td>
                                    <t t-esc="i"/>
                                </td>
                                <td>
                                    <t t-esc="line['partner_id']"/>
                                </td>
                                <td>
                                    <t t-esc="line['checkin_date']"/>
                                </td>
                                <td>
                                    <t t-esc="line['checkin_date']"/>
                                </td>
                                <td>
                                    <t t-esc="line['name']"/>
                                </td>
                                <td>
                                    <t t-esc="line['amount_total']"/>
                                </td>
                            </tr>
                        </t>
                    </table>
                </div>
            </t>
        </t>
    </template>
</odoo>
