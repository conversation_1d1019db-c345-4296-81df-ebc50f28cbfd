<?xml version="1.0" encoding="UTF-8"?>
<odoo>
    <!--    Hotel Services Tree View-->
    <record id="hotel_service_view_tree" model="ir.ui.view">
        <field name="name">hotel.service.view.tree</field>
        <field name="model">hotel.service</field>
        <field name="arch" type="xml">
            <list editable="bottom">
                <field name="name"/>
                <field name="unit_price"/>
                <field name="taxes_ids" widget="many2many_tags"/>
            </list>
        </field>
    </record>
    <!--    Services Menu Action-->
    <record id="hotel_service_action" model="ir.actions.act_window">
        <field name="name">Services</field>
        <field name="res_model">hotel.service</field>
        <field name="view_mode">list</field>
        <field name="help" type="html">
            <p class="oe_view_nocontent_create">
                No Services found ! Let's create one
            </p>
        </field>
    </record>
    <!--    Services Menu-->
    <menuitem id="hotel_service_menu"
              name="Services"
              action="hotel_service_action"
              parent="hotel_config_menu"
              sequence="40"/>
</odoo>
