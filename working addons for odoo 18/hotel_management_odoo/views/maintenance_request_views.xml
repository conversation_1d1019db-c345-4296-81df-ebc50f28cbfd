<?xml version="1.0" encoding="UTF-8" ?>
<odoo>
    <!--    Maintenance Request Form View-->
    <record id="maintenance_request_view_form" model="ir.ui.view">
        <field name="name">maintenance.request.view.form</field>
        <field name="model">maintenance.request</field>
        <field name="arch" type="xml">
            <form>
                <header>
                    <button name="action_assign_team" string="Assign To Team"
                            invisible="state != 'draft'"
                            type="object"
                            class="btn-primary"/>
                    <field name="state" widget="statusbar"
                           statusbar_visible="draft,ongoing,done"/>
                </header>
                <sheet>
                    <div>
                        <h2>
                            <field name="sequence"/>
                        </h2>
                    </div>
                    <group>
                        <group>
                            <field name="date"/>
                            <field name="type"/>
                        </group>
                        <group>
                            <field name="team_id"/>
                            <field name="team_head_id"/>
                        </group>
                    </group>
                </sheet>
            </form>
        </field>
    </record>
    <!--    Maintenance Request Menu Action-->
    <record id="maintenance_request_action" model="ir.actions.act_window">
        <field name="name">Maintenance Request</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">maintenance.request</field>
        <field name="view_mode">list,form</field>
    </record>
    <!--    Maintenance Request Menu-->
    <menuitem id="maintenance_request_menu"
              name="Maintenance Request"
              parent="maintenance_menu"
              action="maintenance_request_action"/>
</odoo>
