<?xml version="1.0" encoding="UTF-8"?>
<templates id="template" xml:space="preserve">
    <!-- Extend the PaymentScreen template -->
    <t t-name="PaymentScreen" t-inherit="point_of_sale.PaymentScreen" owl="1">
        <!-- Add the Room button to the payment methods section -->
        <xpath expr="//div[@class='payment-methods-container']" position="before">
            <div class="hotel-reservation-section p-2 border-bottom">
                <div class="d-flex align-items-center justify-content-between">
                    <button class="btn btn-primary hotel-reservation-btn"
                            t-on-click="selectHotelReservation">
                        <i class="fa fa-bed me-2"></i>
                        Room
                    </button>

                    <!-- Show selected reservation info -->
                    <div t-if="getCurrentReservation()" class="selected-reservation-info">
                        <small class="text-success">
                            <i class="fa fa-check me-1"></i>
                            <strong t-esc="getCurrentReservation().name"/>
                            <br/>
                            <span t-esc="getCurrentReservation().partner_id[1]"/>
                        </small>
                    </div>

                    <!-- Show no reservation selected -->
                    <div t-else="" class="no-reservation-info">
                        <small class="text-muted">
                            No room selected
                        </small>
                    </div>
                </div>
            </div>
        </xpath>
    </t>
</templates>