/** @odoo-module **/

import { PaymentScreen } from "@point_of_sale/app/screens/payment_screen/payment_screen";
import { patch } from "@web/core/utils/patch";
import { HotelReservationSelectionPopup } from "./HotelReservationSelectionPopup";
import { useService } from "@web/core/utils/hooks";

patch(PaymentScreen.prototype, {
    setup() {
        super.setup();
        this.popup = useService("popup");
    },

    async selectHotelReservation() {
        try {
            const { confirmed, payload } = await this.popup.add(HotelReservationSelectionPopup, {
                title: 'Select Hotel Reservation',
            });

            if (confirmed && payload) {
                console.log('Selected reservation:', payload);
                // Set the reservation ID in the current order
                this.pos.get_order().set_reservation_id(payload.id);
                this.render();
            }
        } catch (error) {
            console.error('Error selecting hotel reservation:', error);
        }
    },

    // Get current reservation for display
    getCurrentReservation() {
        const order = this.pos.get_order();
        return order ? order.get_reservation() : null;
    }
});