# -*- coding: utf-8 -*-
{
    'name': "Hotel-Restaurant Management",

    'summary': """
            Module to integrate pos module with hotel
        """,

    'description': """
           """,

    'author': "Trigonal Technology",
    'website': "https://trigonaltechnology.com/",

    # Categories can be used to filter modules in modules listing
    # Check https://github.com/odoo/odoo/blob/13.0/odoo/addons/base/data/ir_module_category_data.xml
    # for the full list
    'category': 'Point of Sale',
    'version': '1.0',

    # any module necessary for this one to work correctly

    'depends': ["point_of_sale", "pos_restaurant", "sale", "hotel_core"],

    # always loaded
    'data': [
        # 'views/hotel_restaurant.xml',
        # 'views/pos_config_view.xml'
    ],
    'assets': {
        'web.assets_backend': [
            'hotel_pos_restaurant/static/src/js/hotel.js',
            'hotel_pos_restaurant/static/src/js/HotelClientListScreen.js',
            'hotel_pos_restaurant/static/src/js/HotelClientLine.js',
            'hotel_pos_restaurant/static/src/js/models.js',
            'hotel_pos_restaurant/static/src/js/PaymentScreen.js',
        ],
        'point_of_sale.assets': [
            # 'order_merge/static/src/css/pos.css',
        ],
        'web.assets_qweb': [
            'hotel_pos_restaurant/static/src/xml/hotel.xml',
            'hotel_pos_restaurant/static/src/xml/HotelClientListScreen.xml',
            'hotel_pos_restaurant/static/src/xml/HotelClientLine.xml',
        ],
    },

    'installable': True,
    'auto_install': False,
    'application': True
}
