<?xml version="1.0" ?>
<odoo>
    <record id="view_pos_config_form" model="ir.ui.view">
        <field name="name">pos.config.form.view.inherit</field>
        <field name="model">pos.config</field>
        <field name="inherit_id" ref="point_of_sale.pos_config_view_form"/>
        <field name="arch" type="xml">
            <xpath expr="//h2[@name='order']" position="before">
                <h2 name="refunds">Hotel Join</h2>
                <div class="row mt16 o_settings_container" id="merge">
                    <div class="col-12 col-lg-6 o_setting_box">
                        <div class="o_setting_left_pane">
                            <field name="iface_hotel_merge"/>
                        </div>
                        <div class="o_setting_right_pane">
                            <label for="iface_hotel_merge" />
                        </div>
                    </div>
                    <div class="col-12 col-lg-6 o_setting_box">
                        <div class="o_setting_lelf_pane">
                            <label for="iface_hotel_payment" string="Payment" />
                            <div class="text-muted">
                                Default partner to use if none is set
                            </div>
                            <div class="content-group mt16">
                                <field name="iface_hotel_payment" />
                            </div>
                        </div>
                    </div>
                </div>
            </xpath>
        </field>
    </record>
</odoo>
