<?xml version="1.0" encoding="UTF-8"?>
<templates id="template" xml:space="preserve">
	<t t-inherit="point_of_sale.PaymentScreen" t-inherit-mode="extension">
       	<xpath expr="//div[hasclass('payment-controls')]" position="inside">
			<div>
                <div class="button js_invoice" t-att-class="{ highlight: env.pos.get_order().get_current_room() }"
                     t-on-click="selectClientHotel">
                    <i class="fa fa-building" role="img" aria-label="Customer"
                       title="Customer"/>
                        <t t-if="env.pos.get_order().get_current_room()">
                            Room: <t t-esc="env.pos.get_order().get_current_room().display_name"/>
                        </t>
                        <t t-if="!env.pos.get_order().get_current_room()">
                            Transfer to Room
                        </t>
                </div>
            </div>
       	</xpath>
	</t>
</templates>