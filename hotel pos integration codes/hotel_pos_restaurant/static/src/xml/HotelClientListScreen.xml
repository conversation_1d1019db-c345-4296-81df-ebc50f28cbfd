<?xml version="1.0" encoding="UTF-8"?>
<templates id="template" xml:space="preserve">

    <t t-name="HotelClientListScreen" owl="1">
        <div class="clientlist-screen screen">
            <div class="screen-content">
                <div class="top-content">
                    <div class="button back" t-on-click="back">
                        <i class="fa fa-angle-double-left"/>
                        <t t-if="!env.isMobile"> Back</t>
                    </div>
<!--                    <div t-if="!state.detailIsShown &amp;&amp; !state.selectedClient" class="button new-customer" role="img" aria-label="Add a customer"-->
<!--                            t-on-click="trigger('activate-edit-mode', { isNewClient: true })"-->
<!--                            title="Add a customer">-->
<!--                        <i class="fa fa-plus"/>-->
<!--                        <t t-if="!env.isMobile"> Create</t>-->
<!--                    </div>-->
                    <div t-if="isNextButtonVisible" t-on-click="clickNext"
                          class="button next highlight">
                        <t t-if="!env.isMobile">
                            <t t-esc="nextButton.text" />
                        </t>
                        <t t-else="">
                            <i t-if="nextButton.command === 'deselect'" class="fa fa-trash"/>
                            <i t-elif="nextButton.command === 'set'" class="fa fa-check"/>
                        </t>
                    </div>
                    <div t-if="!state.detailIsShown" class="searchbox-client top-content-center">
                        <input placeholder="Search Customers" size="1" t-on-keyup="updateRoomList" />
                        <span class="search-clear-client"></span>
                    </div>
                </div>
                <section class="full-content">
                    <div class="client-window">
                        <section class="subwindow list">
                            <div class="subwindow-container">
                                <div t-if="!state.detailIsShown" class="subwindow-container-fix scrollable-y">
                                    <table class="client-list">
                                        <thead>
                                            <tr>
                                                <th>Name</th>
                                                <th t-if="!env.isMobile">Floor</th>
                                                <th t-if="!env.isMobile">Hotel</th>
                                                <th t-if="env.isMobile">Company</th>
                                                <th t-if="!env.isMobile">Reservation</th>
                                                <th class="client-line-email">Customer</th>
                                                <th class="client-line-last-column-placeholder oe_invisible"></th>
                                            </tr>
                                        </thead>
                                        <tbody class="client-list-contents">
                                            <t t-foreach="rooms" t-as="room"
                                               t-key="room.id">
                                                <HotelClientLine partner="room"
                                                            selectedClient="state.selectedRoom"
                                                            detailIsShown="state.detailIsShown"
                                                            t-on-click-client="clickClient" />
                                            </t>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </section>
                    </div>
                </section>
            </div>
        </div>
    </t>

</templates>
