odoo.define('hotel_pos_restaurant.PaymentScreen', function (require) {
    "use strict";

    const PaymentScreen = require('point_of_sale.PaymentScreen');
    const Registries = require('point_of_sale.Registries');
    const framework = require("web.framework");


    const PosValidatePaymentScreen = PaymentScreen => class extends PaymentScreen {
        //@Override
        async validateOrder(isForceValidate) {
            let cOrder = this.env.pos.get_order();
            let order_state = 'invoiced';


            if (this.env.pos.get_order().selectedRoom) {
                let reservation = await this.rpc({
                    model: 'hotel.reservation',
                    method: 'get_checked_in_list',
                    args: []
                });
                if(reservation===undefined || (reservation && !reservation.filter((reserv)=>reserv.id===cOrder.selectedRoom.reservation.id).length)){
                    await this.showPopup('ErrorPopup', {
                            title: this.env._t('Reservation not valid'),
                            body: this.env._t('Please Select active Hotel Room')
                        });
                    cOrder.selectedRoom=null;
                    return
                }
                if (super.currentOrder.get_due() || !super.currentOrder.paymentlines.length) {
                    if (!this.env.pos.config.iface_hotel_payment[0]) {
                        await this.showPopup('ErrorPopup', {
                            title: this.env._t('Error'),
                            body: this.env._t('No Default Hotel Payment Configured')
                        });
                    }
                    else {
                        let paymentMethod = this.payment_methods_from_config.find((payment) => payment.id == this.env.pos.config.iface_hotel_payment[0]);
                        this.addNewPaymentLine({detail: paymentMethod})
                    }
                    order_state = 'draft'
                }
            }
            await super.validateOrder(isForceValidate);
            if (cOrder.paymentlines.length && cOrder.changed.client && !super.currentOrder.get_due()) {
                await this.createSalesOrder(cOrder.export_as_JSON(), order_state);
            }

            // return

        }

        async createSalesOrder(currentOrderJSON, order_state) {
            // framework.blockUI();
            let sales_id = await this.rpc({
                model: 'sale.order',
                method: 'create_order_from_pos',
                args: [currentOrderJSON, order_state]
            });
            // framework.unblockUI();
        }

    };

    Registries.Component.extend(PaymentScreen, PosValidatePaymentScreen);

    return PosValidatePaymentScreen;
});
