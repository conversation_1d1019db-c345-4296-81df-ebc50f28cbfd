odoo.define('hotel_pos_restaurant.HotelClientLine', function(require) {
    'use strict';

    const PosComponent = require('point_of_sale.PosComponent');
    const Registries = require('point_of_sale.Registries');

    class HotelClientLine extends PosComponent {
        get highlight() {
            return this.props.partner !== this.props.selectedClient ? '' : 'highlight';
        }
    }
    HotelClientLine.template = 'HotelClientLine';

    Registries.Component.add(HotelClientLine);

    return HotelClientLine;
});
