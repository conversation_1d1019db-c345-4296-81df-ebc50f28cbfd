odoo.define('hotel_pos_restaurant.HotelClientListScreen', function (require) {
    'use strict';

    const {debounce} = owl.utils;
    const PosComponent = require('point_of_sale.PosComponent');
    const Registries = require('point_of_sale.Registries');
    const {useListener} = require('web.custom_hooks');
    const {isConnectionError} = require('point_of_sale.utils');
    const {useAsyncLockedMethod} = require('point_of_sale.custom_hooks');
    var utils = require('web.utils');

    /**
     * Render this screen using `showTempScreen` to select room.
     * When the shown screen is confirmed ('Set room' or 'Deselect Room'
     * button is clicked), the call to `showTempScreen` resolves to the
     * selected client. E.g.
     *
     * ```js
     * const { confirmed, payload: selectedRoom } = await showTempScreen('HotelClientListScreen');
     * if (confirmed) {
     *   // do something with the selectedRoom
     * }
     * ```
     *
     * @props client - originally selected client
     */
    class HotelClientListScreen extends PosComponent {
        constructor() {
            super(...arguments);
            this.lockedSaveChanges = useAsyncLockedMethod(this.saveChanges);
            useListener('click-save', () => this.env.bus.trigger('save-customer'));
            // useListener('click-edit', () => this.editClient());
            useListener('save-changes', this.lockedSaveChanges);

            // We are not using useState here because the object
            // passed to useState converts the object and its contents
            // to Observer proxy. Not sure of the side-effects of making
            // a persistent object, such as pos, into owl.Observer. But it
            // is better to be safe.

            this.state = {
                query: null,
                selectedRoom: this.props.room,
                detailIsShown: false,
                isEditMode: false,
                activeRooms: this.props.activeRooms,
            };
            this.updateRoomList = debounce(this.updateRoomList, 70);
        }

        // Lifecycle hooks
        back() {
            if (this.state.detailIsShown) {
                this.state.detailIsShown = false;
                this.render();
            } else {
                this.props.resolve({confirmed: false, payload: false});
                this.trigger('close-temp-screen');
            }
        }

        confirm() {
            this.props.resolve({confirmed: true, payload: this.state.selectedRoom});
            this.trigger('close-temp-screen');
        }

        // Getters

        get currentOrder() {
            return this.env.pos.get_order();
        }

        get rooms() {
            let res;
            if (this.state.query && this.state.query.trim() !== '') {
                res = this.env.pos.search_room(this.state.query.trim());
            } else {
                let rooms = [];
                let search_string='';
                this.state.activeRooms.forEach((reservation) => {
                    reservation.rooms.forEach((room_id) => {
                        this.env.pos.rooms_by_id[room_id]['reservation'] = {
                            'id': reservation.id,
                            'name': reservation.name
                        };
                        this.env.pos.rooms_by_id[room_id]['partner'] = this.env.pos.db.get_partner_by_id(reservation.partner_id);

                        let room = this.env.pos.rooms_by_id[room_id];
                        let searchIndex = this.env.pos.rooms.findIndex((i) => i.id == room.id);
                        this.env.pos.rooms[searchIndex].partner = room.partner;
                        this.env.pos.rooms[searchIndex].reservation = room.reservation;
                        search_string += utils.unaccent(this.env.pos._room_search_string(room));
                        rooms.push(room);
                    });
                });
                this.env.pos.room_search_string = search_string;
                res = rooms;
            }
            return res;

            // return res.sort(function (a, b) { return (a.name || '').localeCompare(b.name || '') });
        }

        get isNextButtonVisible() {
            return this.state.selectedRoom ? true : false;
        }

        /**
         * Returns the text and command of the next button.
         * The command field is used by the clickNext call.
         */
        get nextButton() {
            if (!this.props.room) {
                return {command: 'set', text: this.env._t('Set Room')};
            } else if (this.props.activeRooms && this.props.room === this.state.selectedRoom) {
                return {command: 'deselect', text: this.env._t('Deselect Room')};
            } else {
                return {command: 'set', text: this.env._t('Change Room')};
            }
        }

        // Methods

        // We declare this event handler as a debounce function in
        // order to lower its trigger rate.
        async updateRoomList(event) {
            this.state.query = event.target.value;
            const rooms = this.rooms;
            if (event.code === 'Enter' && rooms.length === 1) {
                this.state.selectedRoom = rooms[0];
                this.clickNext();
            } else {
                this.render();
            }
        }

        clickClient(event) {
            let room = event.detail.client;
            if (this.state.selectedRoom === room) {
                this.state.selectedRoom = null;
            } else {
                this.state.selectedRoom = room;
            }
            this.render();
        }

        clickNext() {
            this.state.selectedRoom = this.nextButton.command === 'set' ? this.state.selectedRoom : null;
            this.confirm();
        }

        cancelEdit() {
            this.deactivateEditMode();
        }
    }

    HotelClientListScreen.template = 'HotelClientListScreen';

    Registries.Component.add(HotelClientListScreen);

    return HotelClientListScreen;
});
