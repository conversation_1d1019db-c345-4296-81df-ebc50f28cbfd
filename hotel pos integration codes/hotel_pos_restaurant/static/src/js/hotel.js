odoo.define('hotel_pos_restaurant.PaymentScreenButton', function (require) {
    'use strict';
    const {Gui} = require('point_of_sale.Gui');
    const PosComponent = require('point_of_sale.PosComponent');
    const {posbus} = require('point_of_sale.utils');
    const ProductScreen = require('point_of_sale.ProductScreen');
    const {useListener} = require('web.custom_hooks');
    const Registries = require('point_of_sale.Registries');
    const PaymentScreen = require('point_of_sale.PaymentScreen');
    const CustomButtonPaymentScreen = (PaymentScreen) =>
        class extends PaymentScreen {
            constructor() {
                super(...arguments);
            }

            async selectClientHotel() {
                // click_hotel
                let reservation = await this.rpc({
                    model: 'hotel.reservation',
                    method: 'get_checked_in_list',
                    args: []
                });
                if (reservation === undefined) {
                    this.currentOrder.selectedRoom = undefined;
                    await this.showPopup('ErrorPopup', {
                        title: this.env._t('Error Reservation'),
                        body:this.env._t('No Active Reservation Found')
                    });
                } else {
                    let selectedRoom = this.env.pos.get_order().selectedRoom;
                    const {confirmed, payload: newRoom} = await this.showTempScreen(
                        'HotelClientListScreen',
                        {
                            room: selectedRoom,
                            activeRooms: reservation
                        }
                    );

                    if (confirmed) {
                        if (newRoom) {
                            this.env.pos.get_order().set_client(newRoom.partner);
                            this.env.pos.get_order().selectedRoom = newRoom;
                            this.env.pos.get_order().reservation_id = newRoom.reservation[0];
                            this.env.pos.get_order().room_id = newRoom.id;
                            this.env.pos.get_order().is_hotel = newRoom.is_hotel;
                            this.env.pos.get('orders').models.forEach((order) => {
                                if (this.env.pos.get_order().name === order.name) {
                                    this.env.pos.db.save_unpaid_order(order);
                                }
                            });
                        } else {
                            this.env.pos.get_order().set_client(null);
                            this.env.pos.get_order().selectedRoom = null;
                            this.env.pos.get_order().reservation_id = null;
                            this.env.pos.get_order().room_id = null;
                            this.env.pos.get_order().is_hotel = null;
                            this.env.pos.get('orders').models.forEach((order) => {
                                if (this.env.pos.get_order().name === order.name) {
                                    this.env.pos.db.save_unpaid_order(order);
                                }
                            });
                        }

                    }
                }
            }
        };
    Registries.Component.extend(PaymentScreen, CustomButtonPaymentScreen);
    return CustomButtonPaymentScreen;
});