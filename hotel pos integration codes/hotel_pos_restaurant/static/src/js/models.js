odoo.define('hotel_pos_restaurant.model', function (require) {
    "use strict";

    var models = require('point_of_sale.models');
    var utils = require('web.utils');

    models.load_models([{
        model: 'hotel.room',
        fields: ['company_id', 'floor_id', 'product_id', 'room_categ_id', 'hotel_id', 'display_name', 'product_tmpl_id', 'is_room', 'status'],
        domain: function (self) {
            return [];
        },
        loaded: function (self, rooms) {
            self.rooms = rooms;
            let temp = {};
            let search_string = '';
            rooms.forEach(function (room) {
                temp[room.id] = room;
                search_string += utils.unaccent(self._room_search_string(room));
            });
            self.rooms_by_id = temp;
            self.room_search_string = search_string;
        }
    }]);

    var posmodel_super = models.PosModel.prototype;
    models.PosModel = models.PosModel.extend({
        initialize: function () {
            posmodel_super.initialize.apply(this, arguments);
        },
        get_room_by_id: function (id) {
            return this.rooms_by_id[id];
        },
        _room_search_string: function (room) {
            var str = room.display_name;
            if (room.floor_id) {
                str += '|' + room.floor_id[1];
            }
            // if (room.company_id !== undefined) {
            //     str += '|' + room.company_id[1];
            // }
            // if (room.room_categ_id) {
            //     str += '|' + room.room_categ_id[1];
            // }
            if (room.partner) {
                str += '|' + room.partner.name;
            }
            if (room.reservation) {
                str += '|' + room.reservation.name;
            }
            if (room.hotel_id) {
                str += '|' + room.hotel_id[1];
            }
            str = room.id + ':' + str.replace(/:/g, '') + '\n';
            return str;
        },
        search_room: function (query) {
            this.limit = 100;
            try {
                query = query.replace(/[\[\]\(\)\+\*\?\.\-\!\&\^\$\|\~\_\{\}\:\,\\\/]/g, '.');
                query = query.replace(/ /g, '.+');
                var re = RegExp("([0-9]+):.*?" + utils.unaccent(query), "gi");
            } catch (e) {
                return [];
            }
            var results = [];
            for (var i = 0; i < this.limit; i++) {
                var r = re.exec(this.room_search_string);
                if (r) {
                    var id = Number(r[1]);
                    results.push(this.get_room_by_id(id));
                } else {
                    break;
                }
            }
            return results;
        },

    });
    var _super_order = models.Order.prototype;
    models.Order = models.Order.extend({
        initialize: function () {
            _super_order.initialize.apply(this, arguments);
            if (this.room_id) {
                this.selectedRoom = this.pos.rooms_by_id[this.room_id];
                this.selectedRoom.reservation = {'id': this.reservation_id, 'name': ''};
            }
        },
        init_from_JSON: function (json) {
            _super_order.init_from_JSON.apply(this, arguments);
            this.reservation_id = json.reservation_id;
            this.is_hotel = json.is_hotel;
            this.room_id = json.room_id;
        },
        export_as_JSON: function () {
            let data = _super_order.export_as_JSON.apply(this, arguments);
            data.reservation_id = this.selectedRoom ? this.selectedRoom.reservation.id : false;
            data.room_id = this.selectedRoom ? this.selectedRoom.id : false;
            data.is_hotel = !!this.selectedRoom;
            return data;
        },
        get_current_room() {
            return this.selectedRoom;
        }
    });
});
