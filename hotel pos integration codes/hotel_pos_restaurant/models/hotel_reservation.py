from datetime import datetime
from odoo import fields, _, models, api
import json
from odoo.exceptions import ValidationError
from datetime import timedelta
import logging

_logger = logging.getLogger(__name__)


class HotelReservation(models.Model):
    _inherit = "hotel.reservation"
    _description = "Hotel reservation model"

    pos_order_ids = fields.One2many('pos.order', 'reservation_id', check_company=True)

    @api.model
    def get_checked_in_list(self):
        domain = []
        rooms = self.env['hotel.reservation'].search([('state', '=', 'checkin')])
        records = self.env['hotel.reservation'].browse(rooms)
        reservation = []
        if records:
            for rec in records:
                # print(self.mapped('partner_id.name'))
                temp = {
                    'id': rec.id.id,
                    'name': rec.id.name,
                    'rooms': [],
                    'partner_id': rec.id.partner_id.id,
                }
                for tmp in rec.id.room_ids.room_id:
                    temp['rooms'].append(tmp.id)
                reservation.append(temp)
            # print(rooms)
            return reservation
