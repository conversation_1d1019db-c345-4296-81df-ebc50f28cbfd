from odoo import api, models, fields
import logging

_logger = logging.getLogger(__name__)


class PosOrder(models.Model):
    _inherit = "pos.order"

    reservation_id = fields.Many2one("hotel.reservation", ondelete="cascade", check_company=True)
    room_id = fields.Many2one("hotel.room", check_company=True, default="")
    is_hotel = fields.Boolean(string="is hotel", default=False)

    def _get_fields_for_draft_order(self):
        fields = super(PosOrder, self)._get_fields_for_draft_order()
        fields.extend([
            'reservation_id',
            'room_id',
            'is_hotel',
        ])
        return fields

    @api.model
    def _order_fields(self, ui_order):
        order_fields = super(PosOrder, self)._order_fields(ui_order)
        order_fields['reservation_id'] = ui_order.get('reservation_id', False)
        order_fields['room_id'] = ui_order.get('room_id', False)
        order_fields['is_hotel'] = ui_order.get('is_hotel', False)
        return order_fields

    @api.model
    def get_table_draft_orders(self, table_id):
        table_orders = super().get_table_draft_orders(table_id)
        for order in table_orders:
            order['reservation_id'] = order['reservation_id'][0] if order['reservation_id'] else ''
            order['room_id'] = order['room_id'][0] if order['room_id'] else ''

        return table_orders

    # def _export_for_ui(self, order):
    #     result = super(PosOrder, self)._export_for_ui(order)
    #     result['reservation_id'] = order.reservation_id.id
    #     return result
