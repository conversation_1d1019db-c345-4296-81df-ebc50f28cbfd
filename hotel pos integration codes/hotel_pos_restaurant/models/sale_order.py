from odoo import _, api, models
import logging

_logger = logging.getLogger(__name__)

class SaleOrder(models.Model):
    _inherit = "sale.order"

    @api.model
    def _prepare_from_pos(self, order_data):
        PosSession = self.env["pos.session"]
        session = PosSession.browse(order_data["pos_session_id"])
        asd = self.env['hotel.reservation'].search([('id', '=', order_data["reservation_id"])]).hotel_id.id
        _logger.info(self.env['hotel.reservation'].search([('id', '=', order_data["reservation_id"])]))
        return {
            "partner_id": order_data["partner_id"],
            "origin": _("Point of Sale %s") % (session.name),
            "client_order_ref": order_data["name"],
            "user_id": order_data["user_id"],
            "pricelist_id": order_data["pricelist_id"],
            "fiscal_position_id": order_data["fiscal_position_id"],
            "reservation_id": order_data["reservation_id"],
            "hotel_id": self.env['hotel.reservation'].search([('id', '=', order_data["reservation_id"])]).hotel_id.id,
            "hotel_order_type": 'restaurant',
        }

    @api.model
    def create_order_from_pos(self, order_data, action):
        SaleOrderLine = self.env["sale.order.line"]

        # Create Draft Sale order
        order_vals = self._prepare_from_pos(order_data)
        sale_order = self.create(order_vals)

        # create Sale order lines
        for order_line_data in order_data["lines"]:
            # Create Sale order lines
            order_line_vals = SaleOrderLine._prepare_from_pos(
                sale_order, order_line_data[2]
            )
            SaleOrderLine.create(order_line_vals)

        # Confirm Sale Order
        if action in ["confirmed", "delivered", "invoiced"]:
            sale_order.action_confirm()

        # mark picking as delivered
        if action in ["delivered", "invoiced"]:
            # Mark all moves are delivered
            for move in sale_order.mapped("picking_ids.move_ids_without_package"):
                move.quantity_done = move.product_uom_qty
            sale_order.mapped("picking_ids").button_validate()

        if action in ["invoiced"]:
            # Create and confirm invoices
            invoices = sale_order._create_invoices()
            invoices.action_post()

            # invoice = self.env['account.invoice'].browse(invoice[0])
            # payment = self.env['account.payment'].create({
            #     'invoice_ids': [(6, 0, [invoice.id])],
            #     'journal_id': journal_id,
            #     'amount': amount,
            #     'payment_date': payment_date,
            #     'payment_type': 'inbound',
            #     'partner_type': 'customer',
            #     'partner_id': invoice.partner_id.id,
            #     'communication': invoice.number,
            # })
            # payment.post()
            # sale_order._apply_invoice_payments()

        return {
            "sale_order_id": sale_order.id,
        }
