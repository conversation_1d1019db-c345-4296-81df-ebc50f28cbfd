from odoo import api, models, fields
import logging

_logger = logging.getLogger(__name__)


class PosOrder(models.Model):
    _inherit = "pos.order"

    # @api.model
    # def create_from_ui(self, orders, draft=False):
    #     order_ids = super(<PERSON><PERSON><PERSON><PERSON><PERSON>, self).create_from_ui(orders, draft)
    #     return order_ids
    #
    # def _get_fields_for_draft_order(self):
    #     fields = super(PosOrder, self)._get_fields_for_draft_order()
    #     fields.extend([
    #         'name',
    #         'no_of_proforma'
    #     ])
    #     return fields
    #
    # @api.model
    # def _order_fields(self, ui_order):
    #     order_fields = super(Pos<PERSON><PERSON>r, self)._order_fields(ui_order)
    #     order_fields['no_of_prints'] = ui_order.get('no_of_prints', False)
    #     order_fields['no_of_proforma'] = ui_order.get('no_of_proforma', False)
    #     return order_fields

    @api.model
    def get_table_draft_orders(self, table_id):
        table_orders = super().get_table_draft_orders(table_id)
        print(table_orders)
        for order in table_orders:
            order['billNo'] = order['name']

        return table_orders

    def _export_for_ui(self, order):
        result = super(PosOrder, self)._export_for_ui(order)
        result['billNo'] = order.name
        return result

    def add_print_no(self, vals):
        self.no_of_prints = vals['no_of_prints']
        print(self.id,self.name, self.pos_reference)
        print(vals)