odoo.define('Direct_Invoice.invoice', function (require) {
    "use strict";

    let model = require('point_of_sale.models')

    let _super_order = model.Order.prototype;
    model.Order = model.Order.extend({
        initialize: function () {
            _super_order.initialize.apply(this, arguments);
            this.to_invoice = true;
            // this.billNo = false;
        },
        is_to_invoice: function () {
            return true;
        },
        init_from_JSON: function(json) {
            _super_order.init_from_JSON.apply(this, arguments);
            this.billNo = json.billNo;
        },
        export_as_JSON: function() {
            let data =_super_order.export_as_JSON.apply(this,arguments);
            data.billNo= this.billNo;
            return data;
        },
        export_for_printing: function(){
            var receipt=_super_order.export_for_printing.apply(this, arguments);
            receipt['billNo'] = this.billNo;
            return receipt;
        },
    });
    let _super_PosModel = model.PosModel.prototype;
    model.PosModel = model.PosModel.extend({
        push_and_invoice_order: function (order) {
            var self = this;
            return new Promise((resolve, reject) => {
                if (!order.get_client()) {
                    reject({code: 400, message: 'Missing Customer', data: {}});
                } else {
                    var order_id = self.db.add_order(order.export_as_JSON());
                    self.flush_mutex.exec(async () => {
                        try {
                            const server_ids = await self._flush_orders([self.db.get_order(order_id)], {
                                timeout: 30000,
                                to_invoice: true,
                            });
                            if (server_ids.length) {
                                const [orderWithInvoice] = await self.rpc({
                                    method: 'read',
                                    model: 'pos.order',
                                    args: [server_ids, ['account_move', 'name']],
                                    kwargs: {load: false},
                                }).then((data) => {
                                    order["billNo"] = data[0]["name"]
                                    return data
                                });
                                ;
                                //This Function is used to create pdf 
                                /*await self
                                    .do_action('account.account_invoices', {
                                        additional_context: {
                                            active_ids: [orderWithInvoice.account_move],
                                        },
                                    })
                                    .catch(() => {
                                        reject({ code: 401, message: 'Backend Invoice', data: { order: order } });
                                    });
                                */
                            } else {
                                reject({code: 401, message: 'Backend Invoice', data: {order: order}});
                            }
                            resolve(server_ids);
                        } catch (error) {
                            reject(error);
                        }
                    });
                }
            });
        },
        initialize: function (attributes) {
            _super_PosModel.initialize.apply(this, arguments);
            this.billNo = null;
        },
    });
    let _super_orderline = model.Orderline.prototype;
    model.Orderline = model.Orderline.extend({
        export_for_printing: function () {
            let line = _super_orderline.export_for_printing.apply(this, arguments);
            line.billNo = this.billNo;
            return line;
        }
    });
});
